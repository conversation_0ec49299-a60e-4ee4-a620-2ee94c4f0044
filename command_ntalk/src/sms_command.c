/************************************************************************
** File Name   : sms_command.c
** Author      : 임동규 (2000/09/21)
** Description : SMS Process Management
** 
************************************************************************/

#include    <stdio.h>
#include    <stdlib.h>
#include    <unistd.h>
#include    <signal.h>
#include    <string.h>
#include    <sys/types.h>
#include    <sys/ipc.h>
#include    <sys/shm.h>
#include    <sys/msg.h>
#include    <errno.h>
#include    <time.h>


#include    <code_info.h>
#include    <process_config.h>
#include    <shm_info.h>


#define     START_ALL               0
#define     START_PROCESS           1
#define     STOP_ALL                2
#define     STOP_PROCESS            3
#define     DISPLAY_ALL             4
#define     DISPLAY_PROCESS         5
#define     DISPLAY_CONTINUE        6
#define     DISPLAY_ACTIVE          7
#define     RESTART_PROCESS         8


int         shared_memory_id;
int         SHMSIZE;

int         INTERVAL_SEC;
char        PS_EF_FILE[256];

FILE*	    process_config_fd;
FILE*	    ps_ef_fd;

/*
extern char *sys_errlist[];
*/
struct      _display_record
{
	char    os_process_no           [ 8];  // Increased from 6 to 8 for 7-digit PID support
	char    filler_1                [ 1];
	char    process_no              [ 6];
	char    filler_2                [ 1];
	char    process_name            [32];
	char    filler_3                [ 1];
	char    process_status          [ 4];
	char    filler_4                [ 1];
	char    real_status             [ 6];
	char    filler_5                [ 1];
	char    start_time              [19];
	char    filler_6                [ 1];
	char    stop_time               [19];
	char    end                     [ 1];
};
struct      _display_record         DR;
struct _shm_info *              shm_info;

int         init_rtn(int argc, char **argv, char * process_no);
void        main_rtn(int ret_value, char * process_no);
void        end_rtn(void);

long        ATOL(char *dst, int len);
char *      itoa_reverse(int id);

void        start_all(void);
void        start_process (int index, struct _process_config * process_config);
void        start_process_no(char *process_no);
int         restart_process(void);
void        display_all(int);
void        display_process(char *process_no);
void        display_process_no(int index);
void        display_continue(void);
void        stop_all(void);
void        stop_process(char *process_no);
void        stop_process_no(int index);

void        help_display(char * cmd);
void        open_ps_ef(void);
void        close_ps_ef(void);
//inline void	get_process_name(char *dst, char *process_name);
static inline void	get_process_name(char *dst, char *process_name);
char *      get_process_id(char *dst);
void        set_shm_area(int, struct _process_config *);
void        get_timestring(char *fmt, long n, char *s);
void        sig_int(int);
int         LTOAN(long src, char *dst, int len);
void        display_title();

/* 프로세스 이름을 KERNEL Format Buffer에서 얻는다. */
static inline void get_process_name(char *dst, char * process_name)
{
#if 0
	int         i;

	for (i = 0; i < 64; i ++)
	{
		if (dst[i+PROCESS_NAME_OFFSET] <= ' ') break;
		process_name[i] = dst[i+PROCESS_NAME_OFFSET];
	}
	process_name[i] = '\0';
#else
	/* 
	 * ps -ef 실행후 8번째 CMD 값 얻어옴
	 * UID        PID  PPID  C STIME TTY          TIME CMD
	 */
    sscanf(dst, "%*s %*s %*s %*s %*s %*s %*s %s", process_name);
#endif
}

/* Start */
int main(int argc, char **argv)
{
	int     ret_value;
	char    process_no[10];

	if (signal(SIGINT, sig_int) == SIG_ERR)
	{
		printf("signal SIGINT error.<%d><%s>\n", errno, strerror(errno));
		exit(1);
	}
	ret_value = init_rtn(argc, argv, process_no);
	main_rtn(ret_value, process_no);
	end_rtn();
	return 0;
}

/* Initialization processing */
int init_rtn(int argc, char **argv, char *process_no)
{
	int     idx, ret_value;

	/* process_config .table file open */
	if ((process_config_fd = fopen(FILEPATH_TABLE, "rb")) == NULL)
	{
		printf("%s fopen error.\n", PROCESS_CONFIG);
		exit(1);
	}

	SHMSIZE = sizeof(struct _sys_info) + sizeof(struct _shm_info);
	/* Get Shared Memory */
	if ((shared_memory_id = shmget((key_t)SYS_SHM_KEY, SHMSIZE, PERMS|IPC_CREAT)) < 0)
	{
		printf("shmget error. %d %s\n", errno, strerror(errno));
		exit(1);
	}
	/* Attach Shared Memory */
	if ((shm_info = (struct _shm_info *)shmat(shared_memory_id, (char *)0, 0)) == (struct _shm_info *)-1)
	{
		printf("shmat error.\n");
		exit(1);
	}

	/* parsing command line */
	if (argc < 3) help_display(argv[0]);
	strcpy(process_no, argv[2]);

	if (strcmp(argv[1], "-s") == 0)
	{
		if (strcmp(argv[2], "all") == 0) return START_ALL;
		ret_value = START_PROCESS;
	}
	else if (strcmp(argv[1], "-t") == 0)
	{
		if (strcmp(argv[2], "all") == 0) return STOP_ALL;
		ret_value = STOP_PROCESS;
	}
	else if (strcmp(argv[1], "-d") == 0)
	{
		if (strcmp(argv[2], "all")      == 0) return DISPLAY_ALL;
		if (strcmp(argv[2], "active")   == 0) return DISPLAY_ACTIVE;
		if (strcmp(argv[2], "continue") == 0)
		{
			if (argv[3] != NULL) INTERVAL_SEC = atoi(argv[3]);
			return DISPLAY_CONTINUE;
		}
		ret_value = DISPLAY_PROCESS;
	}
	else if (strcmp(argv[1], "-r") == 0)
	{
		return RESTART_PROCESS;
	}
	else
	{
		help_display(argv[0]);
	}

	/* process no checking */
	for (idx = 0; process_no[idx] != 0; idx ++)
		if (process_no[idx] < '0' || process_no[idx] > '9')
			help_display(argv[0]);
	return ret_value;
}

/* MAIN Processing */
void main_rtn(int direct, char * process_no)
{
	open_ps_ef();
	switch (direct)
	{
	case START_ALL       : start_all();
		break;
	case START_PROCESS   : start_process_no(process_no);
		break;
	case STOP_ALL        : stop_all();
		break;
	case STOP_PROCESS    : stop_process(process_no);
		break;
	case DISPLAY_ALL     : display_all(DISPLAY_ALL);
		break;
	case DISPLAY_ACTIVE  : display_all(DISPLAY_ACTIVE);
		break;
	case DISPLAY_PROCESS : display_process(process_no);
		break;
	case DISPLAY_CONTINUE: display_continue();
		break;
	case RESTART_PROCESS : restart_process();
		break;
	}
	close_ps_ef();
}

/* Finish Processing */
void end_rtn(void)
{
	fclose(process_config_fd);
	exit(0);
}

/* 표지를 출력한다. */
void display_title()
{
	char temp[32];
	memset(temp,0x00,sizeof(temp));
	get_timestring("%2d/%02d/%02d,%02d:%02d:%02d", time(NULL), temp);
	printf("\n\n%s\n\n", temp);
	printf("OS_PID  PID      이             름     상태 실상태     실 행 시 간         종 료 시 간    \n");
	printf("------ ------ ------------------------ ---- ------ ------------------- -------------------\n");
	return;
}

/* 전체 프로세스를 기동한다. */
void start_all(void)
{
	char    ef_buffer[1024], ef_process_name[64];
	int     be = 0, count = 0;

	/* 실제 기동하고있는 프로세스가 있는가를 확인한다. */
	while (fgets(ef_buffer, sizeof(ef_buffer), ps_ef_fd) != NULL)
	{
		memset(ef_process_name, 0, sizeof(ef_process_name));
		get_process_name(ef_buffer, ef_process_name);
		rewind(process_config_fd);
		while (fread(&process_config, sizeof(struct _process_config), 1, process_config_fd) == 1)
		{
			if (strcmp(ef_process_name, process_config.process_name) == 0)
			{
				be = 1;
				printf("PID : %s, PROCESS_NO : %s, PROCESS_NAME : %s(%s)\n",
				get_process_id(ef_buffer),
				process_config.process_no,
				process_config.process_name,
				process_config.execute_program);
			}
		}
	}
	if (be)
	{
		printf("\n이미 기동중에 있는 프로세가 있습니다.\n");
		printf("모든 프로세스 기동 기능을 수행 할 수 없습니다.\n");
		printf("확인 후 다시 시작하십시요.\n");
		exit(1);
	}

    /* System Information Setting */
	shm_info->sys_info.shm_key       = (key_t)SYS_SHM_KEY;
	shm_info->sys_info.sema_key      = (key_t)SYS_SEMA_KEY;
	shm_info->sys_info.business_date = time(NULL);
	shm_info->sys_info.max_process   = SYS_MAX_PROCESS;

    /* 모든 프로세스를 기동한다. */
	rewind(process_config_fd);
	while (fread(&process_config, sizeof(struct _process_config), 1, process_config_fd) == 1)
	{
		set_shm_area(count, &process_config);
		if (process_config.start_up_flag[0] == '1')
			start_process(count, &process_config);
		count ++;
	}
	shm_info->sys_info.max_process   = count;
}

/* 프로세스를 재시작한다. */
int restart_process(void)
{
	int         count = 0;

	if (shm_info->sys_info.max_process <= 0)
	{
		printf("\n\n... START UP ALL 이전 상태에서는 RESTART할 수 없습니다. ...\n\n");
		return 0;
	}
	rewind(process_config_fd);
	while (fread(&process_config, sizeof(struct _process_config), 1, process_config_fd) == 1)
	{
		if (process_config.start_up_flag[0] == '1')
		{
			if (shm_info->process_info[count].os_process_no <= 0 || kill(shm_info->process_info[count].os_process_no, 0) != 0)
			{
				set_shm_area (count, &process_config);
				start_process(count, &process_config);
			}
		}
		count ++;
	}
	return 0;
}

/* 지정된 프로세스를 기동한다. */
void start_process_no(char *process_no)
{
	int     be = 0, count = 0;
	char    ef_buffer[1024], ef_process_name[64];
	while (fread(&process_config, sizeof(struct _process_config), 1, process_config_fd) == 1)
	{
		if (strcmp(process_no, process_config.process_no) == 0 && process_config.start_up_flag[0] == '1')
		{
			rewind(ps_ef_fd);
			be = 0;
			while (fgets(ef_buffer, sizeof(ef_buffer), ps_ef_fd) != NULL)
			{
				get_process_name(ef_buffer, ef_process_name);
				if (strcmp(ef_process_name, process_config.process_name) == 0 || strcmp(ef_process_name, process_config.execute_program) == 0)
				{
					be = 1;
					printf("PID : %s, PROCESS_NO : %s, PROCESS_NAME : %s(%s)\n",
					get_process_id(ef_buffer),
					process_config.process_no,
					process_config.process_name,
					process_config.execute_program);
				}
			}
			if (be) break;
			set_shm_area(count, &process_config);
			start_process(count, &process_config);
		}
		count ++;
	}
	if (be)
	{
		printf("\n지정된 프로세가 이미 기동중에 있습니다.\n");
		printf("확인 후 다시 시작하십시요.\n");
		exit(1);
	}
}

/* 모든 프로세스의 상태를 표시한다. */
void display_all(int flag)
{
	int     cntProcess = 0, index, line = 1;

	rewind(process_config_fd);
	while (fread(&process_config, sizeof(struct _process_config), 1, process_config_fd) == 1)
	{
		cntProcess ++;
	}
	display_title();
	for (index = 0; index < cntProcess; index ++)
	{
		if (line % 24 == 0)
		{
			printf(">");
			fflush(0);
			sleep(1);
			printf("\b");
		}
		if (flag == DISPLAY_ACTIVE)
		{
			if (shm_info->process_info[index].os_process_no > 0 && kill(shm_info->process_info[index].os_process_no, 0) == 0)
			{
				line ++;
				display_process_no(index);
			}
			continue;
		}
		line ++;
		display_process_no(index);
	}
}

/* 지정된 프로세스의 상태를 표시한다. */
void display_process(char * process_no)
{
	int     cntProcess = 0, index;

	while (fread(&process_config, sizeof(struct _process_config), 1, process_config_fd) == 1)
	{
		cntProcess ++;
	}
	display_title();
	for (index = 0; index < cntProcess; index ++)
	{
		if (strcmp(shm_info->process_info[index].process_no, process_no) == 0)
		{
			display_process_no(index);
			break;
		}
	}
}

/* 프로세스의 상태를 지속적으로 출력한다. */
void display_continue(void)
{
	while (1)
	{
		display_all(DISPLAY_ALL);
		sleep(INTERVAL_SEC);
		if (!INTERVAL_SEC) break;
	}
}

/* 상태를 표시한다. */
void display_process_no(int index)
{
	char     temp[64];

	memset((char *)&DR, ' ', sizeof(struct _display_record));
	LTOAN (shm_info->process_info[index].os_process_no, (char *)DR.os_process_no, 7);  // Changed from 6 to 7 for 7-digit PID support
	memcpy((char *)DR.process_no, shm_info->process_info[index].process_no,
			strlen(shm_info->process_info[index].process_no));
	memcpy((char *)DR.process_name, shm_info->process_info[index].process_name,
			strlen(shm_info->process_info[index].process_name));
	DR.filler_3[0] = ' ';

	if (shm_info->process_info[index].process_status == ACTIVE_STATUS)
		memcpy(temp, "ACT ", 4);
	else if (shm_info->process_info[index].process_status == INACTIVE_STATUS)
		memcpy(temp, "STOP", 4);
	else
		memcpy(temp, "INIT", 4);
		memcpy((char *)DR.process_status, temp, 4);
		DR.filler_4[0] = ' ';

	if (shm_info->process_info[index].os_process_no <= 0)
		memcpy(temp, "INIT", 4);
	else if (kill(shm_info->process_info[index].os_process_no, 0) == 0)
		memcpy(temp, "ACT ", 4);
	else
		memcpy(temp, "STOP", 4);
		memcpy((char *)DR.real_status, temp, 4);
		get_timestring("%4d/%02d/%02d,%02d:%02d:%02d", shm_info->process_info[index].start_time,
			(char *)DR.start_time);
		get_timestring("%2d/%02d/%02d,%02d:%02d:%02d", shm_info->process_info[index].stop_time,
			(char *)DR.stop_time);
		DR.end[0] = '\0';
		printf("%s\n", (char *)&DR);
}

/* 모든 프로세스를 정지한다. */
void stop_all(void)
{
	int     cntProcess = 0, index;

	while (fread(&process_config, sizeof(struct _process_config), 1, process_config_fd) == 1)
	{
		cntProcess ++;
	}

	/* Watchdog 부터 정지 */
	for (index = 0; index < cntProcess; index ++)
	{
		if (shm_info->process_info[index].os_process_no > 0
				&& shm_info->process_info[index].process_type  == ANY_TYPE)
		{
			stop_process_no(index);
		}
	}

	/* 확인 */
	for (index = 0; index < cntProcess; index ++)
	{
		if (shm_info->process_info[index].os_process_no > 0
				&& shm_info->process_info[index].process_type  == ANY_TYPE)
		{
			if (kill(shm_info->process_info[index].os_process_no, 0) == 0)
			{
				printf("%s failed in stopping process.\n",shm_info->process_info[index].process_name);
				exit(1);
			}
		}
	}


	/* Client Process 정지 */
	for (index = 0; index < cntProcess; index ++)
	{
		if (shm_info->process_info[index].os_process_no > 0
				&& shm_info->process_info[index].process_type  == CLIENT_TYPE)
		{
			stop_process_no(index);
		}
	}

	/* 확인 */
	for (index = 0; index < cntProcess; index ++)
	{
		if (shm_info->process_info[index].os_process_no > 0
				&& shm_info->process_info[index].process_type  == CLIENT_TYPE)
		{
			if (kill(shm_info->process_info[index].os_process_no, 0) == 0)
			{
				printf("%s failed in stopping process.\n",shm_info->process_info[index].process_name);
				exit(1);
			}
		}
	}


	/* Server Process 정지 */
	for (index = 0; index < cntProcess; index ++)
	{
		if (shm_info->process_info[index].os_process_no > 0
				&& shm_info->process_info[index].process_type  == SERVER_TYPE)
		{
			stop_process_no(index);
		}
	}

	/* 확인 */
	for (index = 0; index < cntProcess; index ++)
	{
		if (shm_info->process_info[index].os_process_no > 0
				&& shm_info->process_info[index].process_type  == SERVER_TYPE)
		{
			if (kill(shm_info->process_info[index].os_process_no, 0) == 0)
			{
				printf("%s failed in stopping process.\n",shm_info->process_info[index].process_name);
				exit(1);
			}
		}
	}

	/* Logger Process Stopping */
	for (index = 0; index < cntProcess; index ++)
	{
		if (shm_info->process_info[index].os_process_no > 0
				&& shm_info->process_info[index].process_type == LOGGER_TYPE)
		{
			stop_process_no(index);
		}
	}

    /* 확인 */
	for (index = 0; index < cntProcess; index ++)
	{
		if (shm_info->process_info[index].os_process_no > 0
				&& shm_info->process_info[index].process_type == LOGGER_TYPE)
		{
			if (kill(shm_info->process_info[index].os_process_no, 0) == 0)
			{
				printf("%s failed in stopping process.\n",shm_info->process_info[index].process_name);
				exit(1);
			}
		}
	}

	/* Monitor Process Stopping */
	for (index = 0; index < cntProcess; index ++)
	{
		if (shm_info->process_info[index].os_process_no > 0
				&& shm_info->process_info[index].process_type == MONITOR_TYPE)
		{
			stop_process_no(index);
		}
	}
    
	/* 확인 */
	for (index = 0; index < cntProcess; index ++)
	{
		if (shm_info->process_info[index].os_process_no > 0
				&& shm_info->process_info[index].process_type == MONITOR_TYPE)
		{
			if (kill(shm_info->process_info[index].os_process_no, 0) == 0)
			{
				printf("%s failed in stopping process.\n",shm_info->process_info[index].process_name);
				exit(1);
			}
		}
	}
}

/* 지정된 프로세스를 정지시킨다. */
void stop_process(char *process_no)
{
	int     cntProcess = 0, index;

	while (fread(&process_config, sizeof(struct _process_config), 1, process_config_fd) == 1)
	{
		cntProcess ++;
	}
	for (index = 0; index < cntProcess; index ++)
	{
		if (strcmp(shm_info->process_info[index].process_no, process_no) == 0
				&& shm_info->process_info[index].os_process_no > 0)
		{
			stop_process_no(index);
		}
	}
}

/* 프로세스를 정지시킨다. */
void stop_process_no(int index)
{
	int   i = 0, ret = -1;
	printf("[%d] SIGUSR1\n", shm_info->process_info[index].os_process_no);
	ret = kill(shm_info->process_info[index].os_process_no, SIGUSR1);
	if (ret < 0)
	{
		printf("%s kill error. <%d><%s>\n",
		shm_info->process_info[index].process_name, errno, strerror(errno));
		return;
	}
	sleep(1);

	printf("[%d] SIGTERM\n", shm_info->process_info[index].os_process_no);

	ret = -1;
	ret = kill(shm_info->process_info[index].os_process_no, SIGTERM);
	if (ret < 0 && kill(shm_info->process_info[index].os_process_no, 0) == 0)
	{
		printf("%s kill error. <%d><%s>\n",
		shm_info->process_info[index].process_name, errno, strerror(errno));
		return;
	}
	/*sleep(1);

	if (kill(shm_info->process_info[index].os_process_no, 0) == 0)
	{
		for (i = 0; i < 3; i ++)
		{
			printf("[%d] SIGKILL\n", shm_info->process_info[index].os_process_no);
			ret = -1;
			ret = kill(shm_info->process_info[index].os_process_no, SIGKILL);
			if (ret < 0)
			{
				printf("%s kill error. <%d><%s>\n",
				shm_info->process_info[index].process_name, errno, strerror(errno));
			}
			sleep(1);
			if (kill(shm_info->process_info[index].os_process_no, 0) == 0) break;
		}
		if (i >= 3)
		{
			printf("%s STOP FAILED.\n", shm_info->process_info[index].process_name);
			return;
		}
	}*/
	printf("%s STOPPED.\n", shm_info->process_info[index].process_name);
}

/*
 * -------------------------------------------------------------------------- *
 * SUBROUTINES
 * -------------------------------------------------------------------------- *
 */

/* Command Line Parameter Helping */
void help_display(char *cmd)
{
	printf("\n\n");
	printf("===================================================================\n");
	printf("1. 프로세스 기동\n");
	printf("   %s -s all[process_no]\n", cmd);
	printf("2. 프로세스 정지\n");
	printf("   %s -t all[process_no]\n", cmd);
	printf("3. 프로세스 상태표시\n");
	printf("   %s -d [all][process_no][continue seconds][active]\n", cmd);
	printf("4. 프로세스 재시작\n");
	printf("   %s -r all\n", cmd);
	printf("===================================================================\n\n");
	exit(1);
}

/* ps -ef | grep MALL > output_file 작성 & 삭제 */
void open_ps_ef(void)
{
	char    temp[256];
	strcpy(PS_EF_FILE, "ps_ef_file_mall_work_");
	strcat(PS_EF_FILE, itoa_reverse(getpid()));
	unlink(PS_EF_FILE);

	strcpy(temp, "ps -ef > ");
	strcat(temp, PS_EF_FILE);
	system(temp);

	if ((ps_ef_fd = fopen(PS_EF_FILE, "r")) == NULL)
	{
		printf("%s fopen error.\n", PS_EF_FILE);
		exit(1);
	}
}

void close_ps_ef(void)
{
		fclose(ps_ef_fd);
		unlink(PS_EF_FILE);
}


/* 프로세스 ID를 KERNEL Format Buffer에서 얻는다. */
char * get_process_id(char *dst)
{
	static char process_id[16];  // Increased buffer size for 7-digit PID support
	char *start, *end;
	int len;

	// Skip the UID field (first 10 characters + any spaces)
	start = dst;
	while (*start && (*start == ' ' || *start == '\t')) start++;  // Skip leading spaces
	while (*start && *start != ' ' && *start != '\t') start++;    // Skip UID
	while (*start && (*start == ' ' || *start == '\t')) start++;  // Skip spaces after UID

	// Now we should be at the start of the PID field
	end = start;
	while (*end && *end != ' ' && *end != '\t') end++;  // Find end of PID

	// Extract PID
	len = end - start;
	if (len > 0 && len < sizeof(process_id)) {
		memcpy(process_id, start, len);
		process_id[len] = '\0';
		return process_id;  // Return string for compatibility
	}

	// Return "0" if parsing failed
	strcpy(process_id, "0");
	return process_id;
}

/* 프로세스를 기동한다. */
void start_process (int count, struct _process_config * process_config)
{
	int     pid, i;
	/* fork */
	if ((pid = fork()) == 0)
	{
            fclose(ps_ef_fd);
            fclose(process_config_fd);


            if (chdir(process_config->execute_directory) < 0)
            {
                printf("%s chdir error. %d %s\n", process_config->execute_directory, errno, strerror(errno));
                exit(1);
            }
            if (execlp(process_config->execute_program,
                        process_config->process_name,
		process_config->command_line_1,
		process_config->command_line_2,
		process_config->command_line_3,
		process_config->command_line_4,
		process_config->command_line_5,
		(char *)0) < 0)
		{
			printf("%s %s %s execlp error.<%d><%s>\n",
			process_config->process_name, process_config->execute_directory,
			process_config->execute_program, errno, strerror(errno));
			exit(1);
		}
		exit(0);
	}
	else if (pid < 0)
	{
		printf("fork error.\n");
		exit(1);
	}
	/* Process ID Setting */
	shm_info->process_info[count].os_process_no = pid;
	shm_info->process_info[count].start_time    = time(NULL);
    shm_info->process_info[count].ProcessCheckFlag	= 63;

	/* checking child process if running */
	for (i = 0; i < 10; i++)
	{
		if (kill(pid, 0) == 0)
		{
			sleep(1);
			break;
		}
		sleep(1);
	}
	if (i >= 10)
	{
		printf("%s running timeout.\n", process_config->process_name);
		exit(1);
	}
}

/* Configuration File 정보를 SHM에 SET한다. */
void set_shm_area(int count, struct _process_config* process_config)
{
	memcpy(       shm_info->process_info[count].process_no, process_config->process_no,
			sizeof(shm_info->process_info[count].process_no));
	memcpy(       shm_info->process_info[count].process_name, process_config->process_name,
			sizeof(shm_info->process_info[count].process_name));
	memcpy(       shm_info->process_info[count].execute_directory, process_config->execute_directory,
			sizeof(shm_info->process_info[count].execute_directory));
	memcpy(       shm_info->process_info[count].execute_program, process_config->execute_program,
			sizeof(shm_info->process_info[count].execute_program));
	shm_info->process_info[count].process_type       = (int)  ATOL(process_config->process_type,       2);
	shm_info->process_info[count].priority           = (int)  ATOL(process_config->priority,           3);
	shm_info->process_info[count].monitor_process_no = (int)  ATOL(process_config->monitor_process_no, 7);
	shm_info->process_info[count].logger_process_no  = (int)  ATOL(process_config->logger_process_no,  7);
	shm_info->process_info[count].send_group_no      = (int)  ATOL(process_config->send_group_no,      7);
	shm_info->process_info[count].my_q_key           = (key_t)ATOL(process_config->my_q_key,          10);
	shm_info->process_info[count].shm_key            = (key_t)ATOL(process_config->shm_key,           10);
	shm_info->process_info[count].sema_key           = (key_t)ATOL(process_config->sema_key,          10);
	shm_info->process_info[count].process_status     = INIT_STATUS;
}

/* 지정된 문자열의 길이만큼 숫자로 변환한다. */
long ATOL(char *dst, int len)
{
	char    temp[32];
	if (dst[0] == '\0') return 0;
	memset(temp,0x00,sizeof(temp));
	strncpy(temp, dst, len);
	return (atol(temp));
}

/*
 * 숫자를 지정한 길이만큼 문자열로 변환 한다
 * RETURN VALUE -1 : Error, 0 : OK
 */
int LTOAN(long src, char *dst, int len)
{
	short int sign, i, j;
	char      temp[40];

	if (len > (int)sizeof(temp)) return -1;

	memset(temp, ' ', sizeof(temp));
	temp[sizeof(temp)-1] = '0';
	sign = (src < 0) ? -1 : 1;

	src  = src * sign;
	for (i = sizeof(temp) - 1; i >= 0 && src > 0; i--)
	{
		temp[i] = (src % 10) + '0';
		src     = src / 10;
	}
	if (sign < 0) temp[i] = '-';

	/* 출력영역으로 출력한다 */
//	for ((int)i = (int)sizeof(temp) - len, j = 0; (int)i < (int)sizeof(temp); i++, j++)
	for (i = sizeof(temp) - len, j = 0; i < sizeof(temp); i++, j++)
	{
		dst[j] = temp[i];
	}
	return 0;
}

/* 시간 문자열을 출력한다. */
void get_timestring(char *fmt, long n, char *s)
{
	struct tm *localt;
	localt = localtime((const time_t *)&n);
	sprintf(s, fmt,
		localt->tm_year + 1900,
		localt->tm_mon + 1,
		localt->tm_mday,
		localt->tm_hour,
		localt->tm_min,
		localt->tm_sec);
	s[strlen(s)] = ' ';
}

/* 숫자를 역순의 문자열로 전환한다. */
char * itoa_reverse(int id)
{
	static char ret[32];
	int         idx = 0;

	memset(ret,0x00,sizeof(ret));
	while (id)
	{
		ret[idx] = '0' + (id % 10);
		id /= 10;
		idx ++;
	}
	return ret;
}

/* SIGINT CALL Routine */
void sig_int(int signo)
{
	printf("BYE !!!\n");
	close_ps_ef();
	end_rtn();
}

/* END */
