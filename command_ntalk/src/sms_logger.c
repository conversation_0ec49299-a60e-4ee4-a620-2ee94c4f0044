/*
 * Project Name : MetaLand 통합 Shopping Mall System
 *
 * Program Name : ml_logger.c
 * Comments     : Process로부터 DATA를 받아 Logging한다.
 * ----------------------------------------------------------------------------
 * History
 *    [ 1] Initial Coding 97.12.23 IM,Song
 * 
 */

#include    <stdio.h>
#include    <stdlib.h>
#include    <unistd.h>
#include    <string.h>
#include    <signal.h>
#include    <errno.h>
#include    <time.h>

#include    <code_info.h>
#include    <message_info.h>
#include    <ml_ctrlsub.h>

#define     MIN(a,b)            ((a < b) ? (a) : (b))
#define     MAX(a,b)            ((a > b) ? (a) : (b))

#define     MD  message_info.msg.s_buffer

char        PROCESS_NO[ 7], PROCESS_NAME[36];
FILE *      oFD;
char		logDir[40];
char        current_date[9];

struct      _message_info   message_info;
struct _shm_info *              shm_info;

static void init_rtn(int argc, char *argv[]);
static int  main_rtn(void);
static void end_rtn(void);
static void sig_rtn(int signo);
static void get_timestring(char *fmt, long n, char *s);
static void monitoring(char *buf, int st, int err);
static int log_open();

/*
 * 작업 시작점
 */
int main (int argc, char *argv[])
{
	oFD = NULL;
    int                 sw = 1;

    if (signal(SIGUSR1, sig_rtn) == SIG_ERR
     || signal(SIGTERM, sig_rtn) == SIG_ERR
     || signal(SIGPIPE, SIG_IGN) == SIG_ERR)
    {
        printf("signal error.<%d><%s>\n", errno, strerror(errno));
        exit(1);
    }

    init_rtn(argc, argv);
    while (sw) {
        main_rtn();
    }
    end_rtn();
    return 0;
}

/*
 * 초기화 처리
 */
static void init_rtn(int argc, char *argv[])
{

    /* 자기의 Information을 구한다. */
    if ( ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0)<0 ) {
        printf("%s ml_sub_init Error.\n", PROCESS_NAME);
        exit(1);
    }
	memset(logDir,0x00,sizeof(logDir));
	strcpy(logDir,argv[1]);
    if (log_open() < 0) {
        ml_sub_end();
        exit(1);
    }
    monitoring("START UP.", 0, 0);
}

/*
 * 주요작업 처리
 */

char* trim( char* szOrg, int leng )
{
	int i = 0;
	for( i=leng-1 ; i>=0 ; i-- ) {
		if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
			szOrg[i] = 0x00;
		}
		else break;
	}
	return szOrg;
}

static int main_rtn(void)
{
	char	buf[128];
	char	date[32];
	char	msgLog[2048];

	if ( ml_sub_recv_all((char*)&message_info, sizeof(message_info), 60)<=0 ) {
		if (errno == EINTR) {
			log_open();
			return 0;
		}
		sprintf(buf, "ml_sub_recv_all failed. %s.\n", strerror(errno));
		monitoring(buf, 0, errno);
		return 0;
	}

	if (log_open()) {
		end_rtn();
	}

	if(oFD) {
		memset(date,0x00,sizeof(date));
				
		//get_timestring("%04d%02d%02d,%02d:%02d:%02d", time(NULL), date);
		get_timestring("%04d%02d%02d,%02d:%02d:%02d:%06ld", time(NULL), date);
		
		memset(msgLog,0x00,sizeof(msgLog));
		memcpy(msgLog,MD.message,MD.msg_length);
		fprintf(oFD,"[%s]:%s\n",trim(date,32),msgLog);
		fflush(oFD);
	}
	return 0;
}

/*
 * 종료작업 처리
 */
static void end_rtn(void)
{
    monitoring("FINISHED.", 0, 0);
	if (oFD != 0) 
	{
		fclose(oFD);
		oFD = NULL;
	}

    ml_sub_end();
    exit(0);
}

static void sig_rtn(int signo)
{
    end_rtn();
}

/* ========================================================================== */
/* Subroutines                                                                */
/* ========================================================================== */

/*
 * 시간 문자열을 출력한다.
 */
static void get_timestring(char *fmt, long n, char *s)
{
    struct tm *localt;
    struct timespec tmv;

    localt = localtime(&n);
    sprintf(s, fmt,
            localt->tm_year + 1900,
            localt->tm_mon + 1,
            localt->tm_mday,
            localt->tm_hour,
            localt->tm_min,
            localt->tm_sec,
            (int)tmv.tv_nsec);
//    s[strlen(s)] = ' ';
}

static int log_open()
{
    char        file[256];
    char date[32];
    char buf[256];

    //get_timestring("%04d%02d%02d,%2d:%2d:%2d", time(NULL), date);
    get_timestring("%04d%02d%02d,%02d:%02d:%02d:%06ld", time(NULL), date);
    
    if (strncmp(date, current_date, 8) == 0) return 0;
	if (oFD != 0) 
	{
		fclose(oFD);
		oFD = NULL;
	}

    strncpy(current_date, date, 8);
    current_date[8] = '\0';
    strcpy(file, logDir);
    strcat(file, PROCESS_NAME);
    strcat(file, ".");
    strcat(file, current_date);
    if ((oFD = fopen(file, "a+b")) == NULL)
    {
        fclose(oFD);
        sprintf(buf,"%s fopen error. %d %s\n", file, errno, strerror(errno));
        monitoring(buf, 0, errno);
        return -1;
    }
    return 0;
}

static void monitoring(char *buf, int st, int err)
{
    if (ml_sub_send_moni(buf, strlen(buf), 3, st, err) <= 0)
    {
        printf("%s ml_sub_send_moni error. %d %s\n", PROCESS_NAME, errno, strerror(errno));
    }
}

/* END */
