/************************************************************************
** File Name   : sms_watchdog.c
** Author      : 임동규 (2000/10/18)
** Description : SMS Process Management
**
************************************************************************/

#include    <stdio.h>
#include    <stdlib.h>
#include    <unistd.h>
#include    <signal.h>
#include    <string.h>
#include    <errno.h>
#include    <dirent.h>
#include    <time.h>
#include    <sys/types.h>
#include    <sys/ipc.h>
#include    <sys/shm.h>
#include    <sys/msg.h>
#include    <sys/stat.h>

#include	<code_info.h>
#include    <process_config.h>
#include    <shm_info.h>
#include    <ml_ctrlsub.h>
#include    <message_info.h>

struct      _message_info       message_info;
_CheckQ     CheckQ;
int         CHECK_Q_ID;
struct _shm_info *              shm_info;

char        PROCESS_NO[ 7], PROCESS_NAME[32];
char        PS_EF_FILE[256];
char        buf[128];
char        gl_block_exception_list[4096];


int         loop_sw = 1;
int         INTERVAL_MIN = 180;

FILE *      process_config_fd;
FILE *      ps_ef_fd;

int         init_rtn(int argc, char **argv);
int         main_rtn(void);
void        end_rtn(void);
void        sig_rtn(int signo);
int         force_start(void);
void        open_process_config(void);
void        close_process_config(void);
void        open_ps_ef(void);
void        close_ps_ef(void);
//inline void	get_process_name(char *dst, char * process_name);
static inline void	get_process_name(char *dst, char * process_name);
char *      get_process_id(char *dst);
void        start_process (int count, struct _process_config * process_config);
void        set_shm_area(int count, struct _process_config* process_config);
long        ATOL(char *dst, int len);
int         LTOAN(long src, char *dst, int len);
void        get_timestring(char *fmt, long n, char *s);
char *      itoa_reverse(int id);
void        monitoring(char *buf, int st, int err);
void        chkBlock();
void        killBlock();
void        getBlockExceptionList();
void        wait_a_moment(int interval);
/*
 * Start
 */

int main(int argc, char **argv)
{	
    time_t ThisT,LastT;
    int nLoop;
    
	process_config_fd = NULL;
	ps_ef_fd = NULL;
	
	signal(SIGCHLD,SIG_IGN);

	if (signal(SIGUSR1, sig_rtn) == SIG_ERR
     || signal(SIGTERM, sig_rtn) == SIG_ERR
     || signal(SIGPIPE, SIG_IGN) == SIG_ERR)
    {
        printf("signal SIGINT error.<%d><%s>\n", errno, strerror(errno));
        exit(1);
    }
    init_rtn(argc, argv);
    time(&LastT);
    

   for(nLoop =0;nLoop < shm_info->sys_info.max_process;nLoop++)
    {
        if( ( (int)shm_info->process_info[nLoop].process_status == 1 ) &&
             ( (int)shm_info->process_info[nLoop].process_type == 3 ) )
        {
            shm_info->process_info[nLoop].ProcessCheckFlag = 63;            
        }
    }
    
    while (loop_sw)
    {
        time(&ThisT);
        if( ((int)difftime(ThisT,LastT) > INTERVAL_MIN) )
        {
            main_rtn();
            killBlock();
            time(&LastT);
        }
        
        if( !((int)difftime(ThisT,LastT) % 2) )
        {
            chkBlock();
        }
        

        wait_a_moment(5000);
    }
    end_rtn();
    return 0;
}

/*
 * Initialization processing
 */
int init_rtn(int argc, char **argv)
{
    if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char *)shm_info, (char *)0) < 0)
    {
        sprintf(buf, "ml_sub_init Error. %s", strerror(errno));
        monitoring(buf, 0, errno);
        ml_sub_end();
        exit(1);
    }

    /* 검사 간격 시간을 분단위로 입력받는다. */
    if (argv[1] != NULL)
    {
        INTERVAL_MIN = (int)ATOL(argv[1], strlen(argv[1])) * 60;
    }

    if (INTERVAL_MIN <= 0) INTERVAL_MIN = 180;
    
    /* check queue key create */
    if ((CHECK_Q_ID = msgget(CHECK_QUEUE_KEY, PERMS | IPC_CREAT)) < 0)
    {
        monitoring("check queue key Create Failed", 0, 0);
        ml_sub_end();
        exit(1);
    }

    monitoring("START UP.", 0, 0);
    return 0;
}

/*
 * MAIN Processing
 */
int main_rtn(void)
{
    open_process_config();
    open_ps_ef();

    force_start();

    close_ps_ef();
    close_process_config();
    return 0;
}

/*
 * Finish Processing
 */
void end_rtn(void)
{
    monitoring("FINISHED.", 0, 0);
    close_process_config();
    close_ps_ef();
    ml_sub_end();
    exit(0);        
}

/*
 * SIGINT CALL Routine
 */
void sig_rtn(int signo)
{
    end_rtn();
}

/*
 * 프로세스 상태를 검사하고 정지된 프로세스를 다시 기동한다.
 */
int force_start(void)
{
    char    ef_buffer[1024], ef_process_name[64];
    int     be, count = 0;

    while (fread(&process_config, sizeof(struct _process_config), 1, process_config_fd) == 1)
    {
        /* SHM영역이 변했는가를 검사한다. */
        if (strcmp(shm_info->process_info[count].process_name, process_config.process_name) != 0)
        {
            sprintf(buf, "Changed SHM AREA or PROCESS_CONFIG.TABLE. Please Check it !!!");
            monitoring(buf, 0, 0);
            loop_sw = 0;
            return -1;
        }

        /* 프로세스가 기동중인가를 KERNEL에서 얻는다. */
        rewind(ps_ef_fd);
        be = 0;
        while (fgets(ef_buffer, sizeof(ef_buffer), ps_ef_fd) != NULL)
        {
            get_process_name(ef_buffer, ef_process_name);
            if (strcmp(ef_process_name, process_config.process_name) == 0)
            {
                be = 1;
                break;
            }
        }
        if (!be && process_config.start_up_flag[0] == '1')
        {
            set_shm_area (count, &process_config);
            start_process(count, &process_config);
        }
        count ++;
    }
    return 0;
}

/*
 * -------------------------------------------------------------------------- *
 * SUBROUTINES
 * -------------------------------------------------------------------------- *
 */

/*
 * process_config .table file open
 */
void open_process_config(void)
{    
	if(process_config_fd)
	{
		fclose(process_config_fd);
		process_config_fd = NULL;
	}
	
    if ((process_config_fd = fopen(PROCESS_CONFIG, "rb")) == NULL)
    {
        sprintf(buf, "%s fopen error. %s\n", PROCESS_CONFIG, strerror(errno));
        monitoring(buf, 0, errno);
    	end_rtn();
    }
}

void close_process_config(void)
{	
    if(process_config_fd)
	{
		fclose(process_config_fd);
		process_config_fd = NULL;
	}
}

/*
 * ps -ef | grep MALL > output_file 작성 & 삭제
 */
void open_ps_ef(void)
{
    char    temp[256];
    strcpy(PS_EF_FILE, "ps_ef_file_mall_work_");
    strcat(PS_EF_FILE, itoa_reverse(getpid()));
    unlink(PS_EF_FILE);

    strcpy(temp, "ps -ef > ");
    strcat(temp, PS_EF_FILE);
    system(temp);
    
	if(ps_ef_fd)
	{
		fclose(ps_ef_fd);
		ps_ef_fd = NULL;
	}

    if ((ps_ef_fd = fopen(PS_EF_FILE, "r")) == NULL)
    {
        sprintf(buf, "%s fopen error. %s\n", PS_EF_FILE, strerror(errno));
        monitoring(buf, 0, errno);
    	end_rtn();
    }
}

void close_ps_ef(void)
{
	if(ps_ef_fd)
	{
		fclose(ps_ef_fd);
		ps_ef_fd = NULL;
	}
    unlink(PS_EF_FILE);
}

/*
 * 프로세스 이름을 KERNEL Format Buffer에서 얻는다.
 */
static inline void get_process_name(char *dst, char * process_name)
{
#if 0
    int         i;

    for (i = 0; i < 64; i ++)
    {
        if (dst[i+PROCESS_NAME_OFFSET] <= ' ') break;
        process_name[i] = dst[i+PROCESS_NAME_OFFSET];
    }
    process_name[i] = '\0';
#else
	/* 
	 * ps -ef 실행후 8번째 CMD 값 얻어옴
	 * UID        PID  PPID  C STIME TTY          TIME CMD
	 */
    sscanf(dst, "%*s %*s %*s %*s %*s %*s %*s %s", process_name);
#endif
}

/*
 * 프로세스 ID를 KERNEL Format Buffer에서 얻는다.
 */
char * get_process_id(char *dst)
{
    static char process_id[16];  // Increased buffer size for 7-digit PID support
    char *start, *end;
    int len;

    // Skip the UID field (first 10 characters + any spaces)
    start = dst;
    while (*start && (*start == ' ' || *start == '\t')) start++;  // Skip leading spaces
    while (*start && *start != ' ' && *start != '\t') start++;    // Skip UID
    while (*start && (*start == ' ' || *start == '\t')) start++;  // Skip spaces after UID

    // Now we should be at the start of the PID field
    end = start;
    while (*end && *end != ' ' && *end != '\t') end++;  // Find end of PID

    // Extract PID
    len = end - start;
    if (len > 0 && len < sizeof(process_id)) {
        memcpy(process_id, start, len);
        process_id[len] = '\0';
        return process_id;  // Return string for compatibility
    }

    // Return "0" if parsing failed
    strcpy(process_id, "0");
    return process_id;
}

/*
 * 프로세스를 기동한다.
 */
void start_process (int count, struct _process_config * process_config)
{

    int     pid, i;

    /*
     * fork
     */

    if ((pid = fork()) == 0)
    {
        fclose(process_config_fd);
		process_config_fd = NULL;
        fclose(ps_ef_fd);
		ps_ef_fd = NULL;

        if (chdir(process_config->execute_directory) < 0)
        {
            printf("%s chdir error. %d %s\n", process_config->execute_directory, errno, strerror(errno));
            exit(1);
        }

        if (execlp(process_config->execute_program,
                   process_config->process_name,
                   process_config->command_line_1,
                   process_config->command_line_2,
                   process_config->command_line_3,
                   process_config->command_line_4,
                   process_config->command_line_5,
                   (char *)0) < 0)
        {
            printf("%s execlp error.<%d><%s>\n",
                    process_config->process_name, errno, strerror(errno));
            exit(1);
        }
        exit(0);
    }
    else
    if (pid < 0)
    {
        sprintf(buf, "fork error. %s\n", strerror(errno));
        monitoring(buf, 0, errno);
    	end_rtn();
        exit(1);
    }
    

    /*
     * Process ID Setting
     */
    shm_info->process_info[count].os_process_no = pid;
    shm_info->process_info[count].start_time    = time(NULL);
    shm_info->process_info[count].ProcessCheckFlag = 63;

    /*
     * checking child process if running
     */
    for (i = 0; i < 10; i++)
    {
        if (kill(pid, 0) == 0)
        {
            sleep(1);
            break;
        }
        sleep(1);
    }
    if (i >= 10)
    {
        sprintf(buf, "%s running timeout.\n", process_config->process_name);
        monitoring(buf, 0, 0);
    }
    else
    {
        sprintf(buf, "%s FORCE START.", process_config->process_name);
        monitoring(buf, 0, 0);
        if ( Alert2Admin("msgbody=%s&type=%d", buf , 0) < 0 )
            monitoring("Alert2Admin Failed...",0,0);
        
    }
}

/*
 * Configuration File 정보를 SHM에 SET한다.
 */
void set_shm_area(int count, struct _process_config* process_config)
{
    memcpy(       shm_info->process_info[count].process_no, process_config->process_no,
           sizeof(shm_info->process_info[count].process_no));
    memcpy(       shm_info->process_info[count].process_name, process_config->process_name,
           sizeof(shm_info->process_info[count].process_name));
    memcpy(       shm_info->process_info[count].execute_directory, process_config->execute_directory,
           sizeof(shm_info->process_info[count].execute_directory));
    memcpy(       shm_info->process_info[count].execute_program, process_config->execute_program,
           sizeof(shm_info->process_info[count].execute_program));
    shm_info->process_info[count].process_type       = (int)  ATOL(process_config->process_type,       2);
    shm_info->process_info[count].priority           = (int)  ATOL(process_config->priority,           3);
    shm_info->process_info[count].monitor_process_no = (int)  ATOL(process_config->monitor_process_no, 7);
    shm_info->process_info[count].logger_process_no  = (int)  ATOL(process_config->logger_process_no,  7);
    shm_info->process_info[count].send_group_no      = (int)  ATOL(process_config->send_group_no,      7);
    shm_info->process_info[count].my_q_key           = (key_t)ATOL(process_config->my_q_key,          10);
    shm_info->process_info[count].shm_key            = (key_t)ATOL(process_config->shm_key,           10);
    shm_info->process_info[count].sema_key           = (key_t)ATOL(process_config->sema_key,          10);
    shm_info->process_info[count].process_status     = INIT_STATUS;
}

/*
 * 지정된 문자열의 길이만큼 숫자로 변환한다.
 */
long ATOL(char *dst, int len)
{
    char    temp[32];

    if (dst[0] == '\0') return 0;
    memset(temp,0x00,sizeof(temp));
    strncpy(temp, dst, len);
    return (atol(temp));
}

/*
 * 숫자를 지정한 길이만큼 문자열로 변환 한다
 * RETURN VALUE -1 : Error, 0 : OK
 */
int LTOAN(long src, char *dst, int len)
{
    short int sign, i, j;
    char      temp[40];

    if (len > (int)sizeof(temp)) return -1;

    memset(temp, ' ', sizeof(temp));
    temp[sizeof(temp)-1] = '0';
    sign = (src < 0) ? -1 : 1;

    src  = src * sign;
    for (i = sizeof(temp) - 1; i >= 0 && src > 0; i--)
    {
        temp[i] = (src % 10) + '0';
        src     = src / 10;
    }
    if (sign < 0) temp[i] = '-';

    /* 출력영역으로 출력한다 */
//    for ((int)i = (int)sizeof(temp) - len, j = 0; (int)i < (int)sizeof(temp); i++, j++)
    for (i = sizeof(temp) - len, j = 0; i < sizeof(temp); i++, j++)
    {
        dst[j] = temp[i];
    }
    return 0;
}

/*
 * 시간 문자열을 출력한다.
 */
void get_timestring(char *fmt, long n, char *s)
{
    struct tm *localt;

    localt = localtime(&n);
    sprintf(s, fmt,
            localt->tm_year + 1900,
            localt->tm_mon + 1,
            localt->tm_mday,
            localt->tm_hour,
            localt->tm_min,
            localt->tm_sec);
    s[strlen(s)] = ' ';
}

/*
 * 숫자를 역순의 문자열로 전환한다.
 */
char * itoa_reverse(int id)
{
    static char ret[32];
    int         idx = 0;

    memset(ret,0x00,sizeof(ret));
    while (id)
    {
        ret[idx] = '0' + (id % 10);
        id /= 10;
        idx ++;
    }
    return ret;
}

/*
 * Monitor process에 전송
 */
void monitoring(char *buf, int st, int err)
{
    if (ml_sub_send_moni (buf, strlen(buf), 3, st, err) <= 0)
    {
        printf("%s ml_sub_send_moni error. %s %d %s\n", PROCESS_NAME, buf, errno, strerror(errno));
    }
}

/* END */

void chkBlock()
{
    int n , nLoop;
    memset(&CheckQ,0x00,sizeof(CheckQ));
    
	CheckQ.mesg_type = 10L;
	n = msgrcv(CHECK_Q_ID, (char*)&CheckQ, sizeof(_CheckQ), CheckQ.mesg_type, IPC_NOWAIT);
	if (n>0)
	{
	    /* get value */	        
        for(nLoop =0;nLoop < shm_info->sys_info.max_process;nLoop++)
        {   
            if( atoi(shm_info->process_info[nLoop].process_no) == atoi(CheckQ.body.ProcessNo) )
            {
                shm_info->process_info[nLoop].ProcessCheckFlag |= CheckQ.body.ProcessCheckFlag;
            }
            
        }	    
	    
	}
   
}


void killBlock()
{
    int  nLoop;    
    char strKillProcess[512];
    char buf[128];
    char logMsg[512];

    getBlockExceptionList();
    for(nLoop =0;nLoop < shm_info->sys_info.max_process;nLoop++)
    {
        if( !(strstr(gl_block_exception_list,shm_info->process_info[nLoop].process_no)) ) 
        {
            if( ( (int)shm_info->process_info[nLoop].process_status == 1 ) &&
                 ( (int)shm_info->process_info[nLoop].process_type == 3 ) )
            {
                if( shm_info->process_info[nLoop].ProcessCheckFlag != 63 )
                {
                    memset(strKillProcess,0x00,sizeof(strKillProcess));
                    sprintf(buf,"[info] pid[%s] IF ( os_pid[%d] < 10 || thread close ) THEN exception ",
                        shm_info->process_info[nLoop].process_no , shm_info->process_info[nLoop].os_process_no );
                    monitoring(buf, 0, 0);
    
                    if( (int)shm_info->process_info[nLoop].os_process_no > 10 )
                    {
                        if ( shm_info->process_info[nLoop].ProcessCheckFlag > 0 )
                        {
                            memset(logMsg,0x00,sizeof(logMsg));
                            sprintf(logMsg,"[%s][%d][info] thread break  not connect",
                                shm_info->process_info[nLoop].process_name,
                                shm_info->process_info[nLoop].ProcessCheckFlag );
                            if ( Alert2Admin("msgbody=%s&type=%d", logMsg, 2) < 0 )
                                monitoring("Alert2Admin Failed...",0,0);
                            else                            
                                monitoring(logMsg,0,0);
                        } else {
                            sprintf(strKillProcess,"kill -9 %d",shm_info->process_info[nLoop].os_process_no);
                            system(strKillProcess);
                            monitoring(strKillProcess, 0, 0);
    
                            memset(logMsg,0x00,sizeof(logMsg));
                            sprintf(logMsg,"[%s][%d] FORCE KILL",
                                shm_info->process_info[nLoop].process_name,
                                shm_info->process_info[nLoop].ProcessCheckFlag );
    
                            if ( Alert2Admin("msgbody=%s&type=%d", logMsg, 0) < 0 )
                                monitoring("Alert2Admin Failed...",0,0);
                            
                        }
                    }
                }
                shm_info->process_info[nLoop].ProcessCheckFlag = 0;            
            }
        }
    }
}

void getBlockExceptionList()
{
    FILE* fp;
    char logMsg[512];
    char tmpbuff[4096];
    
    memset(gl_block_exception_list,0x00,sizeof gl_block_exception_list);
    fp = fopen(FILEPATH_BLOCK_EXCEPTION,"r");
    if( fp == NULL )
    {
        memset(logMsg,0x00,sizeof logMsg);
        sprintf(logMsg,"[%s] not found");
        monitoring(logMsg,0,0);
        return;
    }
    
    memset(tmpbuff,0x00,sizeof tmpbuff);
    while(fgets(tmpbuff,1024,fp))
    {
        if( !(strncmp(tmpbuff,"list:",5)) )
        {
            strcpy(gl_block_exception_list,tmpbuff);
        }
    }
    fclose(fp);
    return;
}


void wait_a_moment(int interval)
{
    struct timeval subtime;
    subtime.tv_sec = 0;
    subtime.tv_usec = interval;
    select(0,(fd_set*)0,(fd_set*)0,(fd_set*)0,&subtime);
}




