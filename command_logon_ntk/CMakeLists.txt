cmake_minimum_required(VERSION 3.10)
project(command_logon_ntk C)

set(CMAKE_C_STANDARD 99)

# CLion build flag configuration (optional feature)
if(NOT DEFINED ENABLE_CLION_BUILD)
    option(ENABLE_CLION_BUILD "Enable CLion-specific build configuration" ON)
endif()

if(ENABLE_CLION_BUILD)
    add_definitions(-DCLION_BUILD)
    message(STATUS "command_logon_ntk: CLion build mode enabled")
endif()

# Define directories
set(INC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/inc)
set(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
set(BIN_DIR ${CMAKE_CURRENT_SOURCE_DIR}/bin)
set(CFG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/cfg)
set(OBJ_DIR ${CMAKE_CURRENT_SOURCE_DIR}/obj)

# Create directories if they don't exist
file(MAKE_DIRECTORY ${BIN_DIR})
file(MAKE_DIRECTORY ${CFG_DIR})
file(MAKE_DIRECTORY ${OBJ_DIR})

# Add include directory
include_directories(${INC_DIR})

# Common source file
set(CTRL_SUB_SRC ${SRC_DIR}/sms_ctrlsub.c)

# Add executables
add_executable(create_table ${SRC_DIR}/create_table.c)
add_executable(sms_command ${SRC_DIR}/sms_command.c)
add_executable(sms_monitor ${SRC_DIR}/sms_monitor.c ${CTRL_SUB_SRC})
add_executable(sms_logger ${SRC_DIR}/sms_logger.c ${CTRL_SUB_SRC})
add_executable(sms_watchdog ${SRC_DIR}/sms_watchdog.c ${CTRL_SUB_SRC})

# Link libraries
target_link_libraries(sms_monitor rt)
target_link_libraries(sms_logger rt)
target_link_libraries(sms_watchdog rt)

# Add custom command to compile sms_ctrlsub.c with g++
add_custom_command(
    OUTPUT ${OBJ_DIR}/sms_ctrlsub++.o
    COMMAND g++ -std=gnu++11 ${CMAKE_C_FLAGS} -I${INC_DIR} -c ${SRC_DIR}/sms_ctrlsub.c -o ${OBJ_DIR}/sms_ctrlsub++.o
    DEPENDS ${SRC_DIR}/sms_ctrlsub.c
    COMMENT "Compiling sms_ctrlsub.c with g++ to create sms_ctrlsub++.o"
)

# Add a custom target to execute the custom command
add_custom_target(
    sms_ctrlsub_cpp ALL
    DEPENDS ${OBJ_DIR}/sms_ctrlsub++.o
)

# Set output paths and names for executables
set_target_properties(create_table PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CFG_DIR})
set_target_properties(create_table PROPERTIES OUTPUT_NAME crt)

set_target_properties(sms_command PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR})
set_target_properties(sms_command PROPERTIES OUTPUT_NAME cmd)

set_target_properties(sms_monitor PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR})
set_target_properties(sms_monitor PROPERTIES OUTPUT_NAME mnt)

set_target_properties(sms_logger PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR})
set_target_properties(sms_logger PROPERTIES OUTPUT_NAME log)

set_target_properties(sms_watchdog PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${BIN_DIR})
set_target_properties(sms_watchdog PROPERTIES OUTPUT_NAME dog)
