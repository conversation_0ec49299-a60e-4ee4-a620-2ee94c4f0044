# CLion-specific CMake configuration for daemon_logon_ntk
# This file provides additional configuration for CLion IDE integration

# Set CLion-specific build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# CLion debugging support
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0 -DDEBUG=5")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2 -DNDEBUG")

# Enable compile_commands.json for better code completion
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# CLion-specific include paths for better code analysis
if(ENABLE_CLION_BUILD)
    # Add additional include paths for CLion IntelliSense
    include_directories(SYSTEM
        /usr/include/c++/11
        /usr/include/x86_64-linux-gnu/c++/11
        /usr/local/include
    )
    
    # Define additional macros for CLion
    add_definitions(
        -D__GNUC__=11
        -D__cplusplus=199711L
        -DLINUX
        -D_GNU_SOURCE
    )
endif()

# Custom targets for CLion
if(ENABLE_CLION_BUILD)
    # Create a target that includes all headers for better indexing
    file(GLOB_RECURSE ALL_HEADERS "${INC_DIR}/*.h" "${INC_DIR}/*.hpp")
    add_custom_target(headers SOURCES ${ALL_HEADERS})
    
    # Create a target for all source files
    file(GLOB_RECURSE ALL_SOURCES 
        "${SRC_DIR}/*.cpp" "${SRC_DIR}/*.c"
        "${LIB_DIR}/*.cpp" "${LIB_DIR}/*.c"
    )
    add_custom_target(sources SOURCES ${ALL_SOURCES})
endif()

# Debugging configuration
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")

# Memory debugging options (uncomment for debugging)
# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address -fno-omit-frame-pointer")
# set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=address")

message(STATUS "CLion CMake configuration loaded")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "CXX Flags: ${CMAKE_CXX_FLAGS}")
