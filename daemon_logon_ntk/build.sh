#!/bin/bash

# Build script for daemon_logon_ntk
# This script automates the CMake build process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check CMake
    if ! command -v cmake &> /dev/null; then
        print_error "CMake is not installed or not in PATH"
        exit 1
    fi
    
    # Check Oracle Pro*C
    if ! command -v proc &> /dev/null; then
        print_warning "Oracle Pro*C (proc) not found in PATH"
        print_warning "Pro*C executables will not be built"
    fi
    
    # Check ORACLE_HOME
    if [ -z "$ORACLE_HOME" ]; then
        print_warning "ORACLE_HOME environment variable not set"
        print_warning "Oracle-dependent executables may fail to build"
    fi
    
    # Check external object file
    EXT_OBJ="../command_logon_ntk/obj/sms_ctrlsub++.o"
    if [ ! -f "$EXT_OBJ" ]; then
        print_warning "External object file not found: $EXT_OBJ"
        print_warning "You may need to build command_logon_ntk first"
    fi
    
    # Check KSkyB libraries
    if [ ! -d "$HOME/library" ]; then
        print_warning "KSkyB library directory not found: $HOME/library"
        print_warning "Custom libraries may not be available"
    fi
    
    print_success "Prerequisites check completed"
}

# Function to create build directory
setup_build_dir() {
    print_status "Setting up build directory..."
    
    if [ -d "build" ]; then
        print_status "Build directory exists, cleaning..."
        rm -rf build
    fi
    
    mkdir build
    cd build
    
    print_success "Build directory created"
}

# Function to configure with CMake
configure_cmake() {
    print_status "Configuring with CMake..."
    
    cmake .. || {
        print_error "CMake configuration failed"
        exit 1
    }
    
    print_success "CMake configuration completed"
}

# Function to build executables
build_executables() {
    print_status "Building executables..."
    
    # Build standard executables
    print_status "Building standard C++ executables..."
    make logonSession logonDB admin monitorProcess adminProcess || {
        print_error "Failed to build standard executables"
        exit 1
    }
    
    # Build Pro*C executables if proc is available
    if command -v proc &> /dev/null && [ -n "$ORACLE_HOME" ]; then
        print_status "Building Oracle Pro*C executables..."
        make senderNtalkProDB reportMMSProcDB || {
            print_warning "Failed to build Pro*C executables"
            print_warning "This may be due to database connectivity issues"
        }
    else
        print_warning "Skipping Pro*C executables (proc not available or ORACLE_HOME not set)"
    fi
    
    print_success "Build completed"
}

# Function to show build results
show_results() {
    print_status "Build results:"
    
    if [ -d "../bin" ]; then
        echo "Executables built in ../bin/:"
        ls -la ../bin/ 2>/dev/null || echo "No executables found"
    else
        print_warning "bin directory not found"
    fi
}

# Function to clean build
clean_build() {
    print_status "Cleaning build..."
    
    if [ -d "build" ]; then
        rm -rf build
        print_success "Build directory removed"
    fi
    
    if [ -d "bin" ]; then
        rm -rf bin
        print_success "bin directory removed"
    fi
    
    if [ -d "obj" ]; then
        rm -rf obj
        print_success "obj directory removed"
    fi
    
    # Remove temporary Pro*C files
    rm -f tp* 2>/dev/null || true
    
    print_success "Clean completed"
}

# Main function
main() {
    echo "daemon_logon_ntk Build Script"
    echo "============================="
    
    case "${1:-build}" in
        "clean")
            clean_build
            ;;
        "build")
            check_prerequisites
            setup_build_dir
            configure_cmake
            build_executables
            show_results
            ;;
        "rebuild")
            clean_build
            check_prerequisites
            setup_build_dir
            configure_cmake
            build_executables
            show_results
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  build    - Build the project (default)"
            echo "  clean    - Clean build artifacts"
            echo "  rebuild  - Clean and build"
            echo "  help     - Show this help message"
            echo ""
            echo "Prerequisites:"
            echo "  - CMake 3.10+"
            echo "  - Oracle Pro*C (for Pro*C executables)"
            echo "  - ORACLE_HOME environment variable"
            echo "  - KSkyB libraries in \$HOME/library"
            echo "  - command_logon_ntk built first"
            ;;
        *)
            print_error "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Change to script directory
cd "$(dirname "$0")"

# Run main function
main "$@"
