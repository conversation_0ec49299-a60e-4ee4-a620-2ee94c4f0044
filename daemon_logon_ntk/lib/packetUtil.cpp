#include "packetUtil.h"


int CPacketUtil::test() 
{
    
}

char* CPacketUtil::findValue(char* szOrg, char* szTag, char* szVal)
{
    char* szStt;          
    char* szEnd;          
    if ((szStt=strstr(szOrg,szTag))) 
	{     
        szStt=szStt+strlen(szTag)+1;   
        szEnd=strstr(szStt,"\r\n");    
        memcpy(szVal,szStt,szEnd-szStt);       
    }
    return szVal;
}

int CPacketUtil::recvMsg()
{
    return 0;
}



