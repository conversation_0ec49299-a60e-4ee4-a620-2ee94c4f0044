/*****************************************************************************
 * File Name   : cust_lib_common.c
 * Author      : <PERSON><PERSON><PERSON>,Lim - kskyb.com
 * Date        : 2006.05.03
 * Description : Log-On(BIND Type) Real-TIME Common Function
 *****************************************************************************/

#include <stdafx.h>
#include <string>
extern struct _message_info message_info;
extern struct _shm_info *shm_info;
extern char   PROCESS_NO[ 7], PROCESS_NAME[36];
extern int    activeProcess;

using namespace std;

void CloseProcess(int sig)
{
    char logMsg[512];
    sprintf(logMsg,"Start & Exit [%d][%d]", getpid(), sig);
    monitoring(logMsg,0,0);
    activeProcess = FALSE;
}

void Init_Server_Fork()
{
    signal( SIGHUP,  CloseProcess );
    signal( SIGCLD,  sig_rtn );
    signal( SIGPIPE, SIG_IGN );
    signal( SIGTERM, CloseProcess );
    signal( SIGINT,  CloseProcess );
    signal( SIGQUIT, CloseProcess );
    signal( SIGKILL, CloseProcess );
    signal( SIGSTOP, CloseProcess );
    signal( SIGUSR1, CloseProcess );
    signal( SIGCHLD, sig_rtn );   
}


void Init_Server()
{
    signal( SIGHUP,  CloseProcess );
    signal( SIGCLD,  SIG_IGN );
    signal( SIGPIPE, SIG_IGN );
    signal( SIGTERM, CloseProcess );
    signal( SIGINT,  CloseProcess );
    signal( SIGQUIT, CloseProcess );
    signal( SIGKILL, CloseProcess );
    signal( SIGSTOP, CloseProcess );
    signal( SIGUSR1, CloseProcess );
    signal( SIGCHLD, SIG_IGN );   
}

void wait_a_moment(int interval)
{
    struct timeval subtime;
    subtime.tv_sec = 0;
    subtime.tv_usec = interval;
    select(0,(fd_set*)0,(fd_set*)0,(fd_set*)0,&subtime);
}


int UnixSockOpenNon(int* sockfd, char* svrIp, int svrPort)
{
    int 	flags;
    int     nSockOpt = 1;
    int     nSndbuf = 49152, nRcvbuf = 49152;
    struct sockaddr_in      serv_addr;
    struct timeval tv;

    memset((void*)&serv_addr ,0x00 ,sizeof(serv_addr));//bzero( (void*)&serv_addr, sizeof(serv_addr) );
     
	serv_addr.sin_family            = AF_INET;
    serv_addr.sin_addr.s_addr       = inet_addr(svrIp);
    serv_addr.sin_port              = htons( svrPort );

    if ((*sockfd=socket(AF_INET,SOCK_STREAM,0))<0) 
		return errno;
    
	if (setsockopt(*sockfd,SOL_SOCKET,SO_REUSEADDR,(char*)&nSockOpt,sizeof(nSockOpt))<0) 
		return errno;
    
	if (setsockopt(*sockfd,SOL_SOCKET,SO_KEEPALIVE,(char*)&nSockOpt,sizeof(nSockOpt))<0) 
		return errno;
    
	if (bind(*sockfd,(struct sockaddr*)&serv_addr,sizeof(serv_addr))<0 ) 
		return errno;
    
	if (setsockopt(*sockfd,SOL_SOCKET,SO_SNDBUF,(char*)&nSndbuf,sizeof(nSndbuf))<0) 
		return errno;
    
	if (setsockopt(*sockfd,SOL_SOCKET,SO_RCVBUF,(char*)&nRcvbuf,sizeof(nRcvbuf))<0) 
		return errno;

    tv.tv_sec = 15;
    tv.tv_usec = 0;
    if (setsockopt(*sockfd,SOL_SOCKET,SO_SNDTIMEO,&tv,sizeof(tv))<0)
   	{
        monitoring("setsockopt : sndtimeo error",0,0);
    }
    if (setsockopt(*sockfd,SOL_SOCKET,SO_RCVTIMEO,&tv,sizeof(tv))<0) 
	{
        monitoring("setsockopt : rcvtimeo error",0,0);
    }

    if ((flags = fcntl(*sockfd,F_GETFL,0))<0) 
		return errno;
    
	flags |= O_NONBLOCK;
    if (fcntl(*sockfd,F_SETFL, flags)<0) 
		return errno;
    
	if ( listen(*sockfd,20)<0 )     
		return errno;
    
	return 0;
}



int UnixSockOpenNon(int* sockfd, int svrPort)
{
    int flags;
    int     nSockOpt = 1;
    int     nSndbuf = 49152, nRcvbuf = 49152;
    struct sockaddr_in      serv_addr;
    struct timeval tv;

    bzero( (void*)&serv_addr, sizeof(serv_addr) );
    serv_addr.sin_family            = AF_INET;
    serv_addr.sin_addr.s_addr       = htonl(INADDR_ANY);
    serv_addr.sin_port                      = htons( svrPort );

    if ((*sockfd=socket(AF_INET,SOCK_STREAM,0))<0) return errno;
    if (setsockopt(*sockfd,SOL_SOCKET,SO_REUSEADDR,(char*)&nSockOpt,sizeof(nSockOpt))<0) return errno;
    if (setsockopt(*sockfd,SOL_SOCKET,SO_KEEPALIVE,(char*)&nSockOpt,sizeof(nSockOpt))<0) return errno;
    if (bind(*sockfd,(struct sockaddr*)&serv_addr,sizeof(serv_addr))<0 ) return errno;
    if (setsockopt(*sockfd,SOL_SOCKET,SO_SNDBUF,(char*)&nSndbuf,sizeof(nSndbuf))<0) return errno;
    if (setsockopt(*sockfd,SOL_SOCKET,SO_RCVBUF,(char*)&nRcvbuf,sizeof(nRcvbuf))<0) return errno;

    tv.tv_sec = 15;
    tv.tv_usec = 0;
    if (setsockopt(*sockfd,SOL_SOCKET,SO_SNDTIMEO,&tv,sizeof(tv))<0) {
         monitoring("setsockopt : sndtimeo error",0,0);
    }
    if (setsockopt(*sockfd,SOL_SOCKET,SO_RCVTIMEO,&tv,sizeof(tv))<0) {
        monitoring("setsockopt : rcvtimeo error",0,0);
    }
 
    if ((flags = fcntl(*sockfd,F_GETFL,0))<0) return errno;
    flags |= O_NONBLOCK;
    if (fcntl(*sockfd,F_SETFL, flags)<0) return errno;
    if ( listen(*sockfd,20)<0 )     return errno;
    return 0;
}

void log_history(int type, int errornum, const char *format, ...)
{
    va_list args;
    char logMsg[SOCKET_BUFF];

    va_start(args, format);
    vsprintf(logMsg, format, args);
    va_end(args);

    if (type==1) logmessage(logMsg,0,0);
    else monitoring(logMsg,0,0);
}



void monitoring(char *buf, int st, int err)
{
    if (ml_sub_send_moni(buf, strlen(buf), 3, st, err) <= 0) {
        printf("%s ml_sub_send_moni error. %d %s\n", PROCESS_NAME, errno, strerror(errno));
    }
}

void logmessage(char *buf, int st, int err)
{
    if (ml_sub_send_log(buf, strlen(buf), 3, st, err) <= 0) {
        printf("%s ml_sub_send_log error. %d %s\n", PROCESS_NAME, errno, strerror(errno));
    }
}

char* disnull(char* szOrg, int leng)
{
    int i = 0;
    for (i=0;leng-1>i;i++) {
        if (szOrg[i]==0x00) {
            szOrg[i] = 0x20;
        }
    }
    return szOrg;
}

char* trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

int chkNum(char *inp)
{
    int i,c;
    for (i=0;i<strlen(inp);i++) {
        c = inp[i];
        if (c<'0'||c>'9') return 0;
    }
    return 1;
}

void viewPackPrint(char *a,int n)
{
    int i;
    char logMsg[VIEWPACK_MAX_SIZE];
    char strtmp[VIEWPACK_MAX_SIZE];

    memset(logMsg,0x00, sizeof logMsg);
    memset(strtmp,0x00, sizeof strtmp);
    for(i=0;i<n;i++)
    {
        if( a[i] == 0x00 )
            strtmp[i] = '.';
        else
            memcpy(strtmp+i,a+i,1);
    }

    printf("info:[%s]",strtmp);
    fflush(stdout);
    return ;
}



void viewPack(char *a,int n)
{
    int i;
    char logMsg[VIEWPACK_MAX_SIZE];
    char strtmp[VIEWPACK_MAX_SIZE];
    memset(logMsg,0x00, sizeof logMsg);
    memset(strtmp,0x00, sizeof strtmp);
    for(i=0;i<n;i++)
    {
        if( a[i] == 0x00 )
            strtmp[i] = '.';
        else
            memcpy(strtmp+i,a+i,1);
    }

    sprintf(logMsg,"info:[%s]",strtmp);
   // monitoring(logMsg,0,0);
   // monitoring("monitoring TTTTT",0,0);
   
    return ;
}

void sig_rtn(int sig)
{
    pid_t childpid;
    do {
        childpid = waitpid( (pid_t) -1, NULL , WNOHANG);
        if( childpid == (pid_t)-1)
            break;
    } while(childpid != (pid_t) 0);
    signal( SIGCHLD, sig_rtn );   
}


int str2int(char* buf, int size)
{                       
    char szTmp[size+1]; 
    memset(szTmp,0x00,size+1);
    memcpy(szTmp,buf,size);
    return atoi(szTmp);
}               


int _monPrint(char* szPath, char* szLog)
{
    static int fd=0;
    int writeLen;

    static char file[256]="";
    char date[32];
    char buff[2048];
    string strBuff;
    string strDate;
    string strLog;
    static char current_date[16]="";

    memset(date,0x00,sizeof(date));
    memset(buff,0x00,sizeof(buff));
    strBuff = "";
    strBuff.reserve(0);

    
    get_timestring2(date);


    if( memcmp(date,current_date,8) == 0 ) {
        /* 이미 있는 파일 */
//        sprintf(buff,"[%s]:%s\n",date,szLog);
        strDate = date;
        strLog = "";
        strLog.reserve(0);
        strLog = szLog;
        strBuff = "[" + strDate + "]:[" + strLog + "]\n"; 

        
        

//        writeLen = write(fd,buff,strlen(buff));
        writeLen = write(fd,(char*)strBuff.c_str(),strBuff.length());
        if( writeLen <= 0 )
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
 //           sprintf(szTmp,"LogWrite file errror !![%s][%s]\n[%s]",
  //                  strerror(errno),file,szLog);
   //         printf(szTmp);
//            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            printf("[%s]\n",strBuff.c_str());
            fflush(stdout);
            close(fd);
            exit(-5);
        }
    } else {
        /* 새로 만들어야 하는 파일 */
        if( fd ) close(fd);
        strncpy(current_date, date, 8);
        current_date[8] = '\0';

/*        strcpy(file, LOGFILEDIR);
 */
        strcpy(file, szPath);
        strcat(file, ".");
        strcat(file, current_date);

        fd = open( file, CREATE_FLAGS, CREATE_PERMS );
        if( fd < 0 ) 
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            /*
            sprintf(szTmp,"log File Open fail : [%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            */
            close(fd);
            exit(-5);
        }

//        sprintf(buff,"[%s]:%s\n",date,szLog);
        strDate = date;
        strLog = "";
        strLog.reserve(0);
        strLog = szLog;
        strBuff = "[" + strDate + "]:[" + strLog + "]\n"; 

        
 

//        writeLen = write(fd,buff,strlen(buff));

        writeLen = write(fd,(char*)strBuff.c_str(),strBuff.length());
        if( writeLen <= 0 )
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            /*
            sprintf(szTmp,"LogWrite file errror !![%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            */
            printf("[%s]\n",strBuff.c_str());
            fflush(stdout);

            close(fd);
            exit(-5);
        }


    }

    return 0;
}



int _logPrint(char* szPath, char* szLog)
{
    static int fd=0;
    int writeLen;

    static char file[256]="";
    char date[32];
    char buff[2048];
    static char current_date[16]="";

    memset(date,0x00,sizeof(date));
    memset(buff,0x00,sizeof(buff));
    get_timestring2(date);


    if( memcmp(date,current_date,8) == 0 ) {
        /* 이미 있는 파일 */
        sprintf(buff,"[%s]:%s\n",date,szLog);

        writeLen = write(fd,buff,strlen(buff));
        if( writeLen <= 0 )
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            sprintf(szTmp,"LogWrite file errror !![%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            close(fd);
            exit(-5);
        }



    } else {
        /* 새로 만들어야 하는 파일 */
        if( fd ) close(fd);
        strncpy(current_date, date, 8);
        current_date[8] = '\0';

/*        strcpy(file, LOGFILEDIR);
 */
        strcpy(file, szPath);
        strcat(file, ".");
        strcat(file, current_date);

        fd = open( file, CREATE_FLAGS, CREATE_PERMS );
        if( fd < 0 ) 
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            sprintf(szTmp,"log File Open fail : [%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            close(fd);
            exit(-5);
        }

        sprintf(buff,"[%s]:%s\n",date,szLog);

        writeLen = write(fd,buff,strlen(buff));
        if( writeLen <= 0 )
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            sprintf(szTmp,"LogWrite file errror !![%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            close(fd);
            exit(-5);
        }


    }

    return 0;
}



/*
 * 시간 문자열을 출력한다.
 */
void get_timestring(char *fmt, long n, char *s)
{
    struct tm *localt;

    localt = localtime(&n);
    sprintf(s, fmt,
            localt->tm_year + 1900,
            localt->tm_mon + 1,
            localt->tm_mday,
            localt->tm_hour,
            localt->tm_min,
            localt->tm_sec);
    s[strlen(s)] = ' ';
}

void get_timestring2(char *s)
{
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);
	sprintf(s, "%04d%02d%02d,%02d:%02d:%02d.%09d",
		tp.tm_year+1900, tp.tm_mon+1, tp.tm_mday,
		tp.tm_hour, tp.tm_min, tp.tm_sec,
		(int)tmv.tv_nsec
		);
    s[strlen(s)] = ' ';
}








