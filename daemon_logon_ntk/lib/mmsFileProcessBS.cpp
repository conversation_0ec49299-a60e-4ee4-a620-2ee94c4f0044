#include "mmsFileProcessBS.h"
#include "ksbase64.h"
#include <sys/stat.h>
#include <sys/types.h>
#include <regex.h>

/*
2008-10-07 TXT , TXT_LGT 파일 생성하지 않도록 수정함
*/

int CMMSFileProcess::write(
        CKSSocket& db,
        CMMSPacketSend& mmsPacketSend,
        char* path,
        long long mmsid ,
        char* szYYYYMM,
        char* senderID,
        int ctnid,
        int timeout,
        char* senderDbDomainName,
        int bBcastFlag)
{
    CMData mData;
    string strData;
    char* pData;
    int size;
    int ret;
    char szType[50+1];
    char szCtnSvc[5+1];
	char classify[32];
	CMMSCtnTbl mmsCtnTbl;


	char szFullFileName[512];
    ret = mmsPacketSend.getMDataFirst(mData);
    if( ret != 0 )
        return 0;
    bKTFTXT = false;

    clearListMMSCtnTbl();
    memset(szTxtPath,0x00,sizeof(szTxtPath));

    pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), 
            mData.strData.length(), &size);

    printf("decode [%s]------ [%s]\n",
            szYYYYMM,
            pData);
    fflush(stdout);

    memset(szType,0x00,sizeof(szType));
    memset(szCtnSvc,0x00,sizeof(szCtnSvc));

    sprintf(szType,(char*)mData.contentType.strType.c_str());
    sprintf(szCtnSvc,(char*)mData.strSvc.c_str());

    classifyMData(path,mmsid,szYYYYMM,pData,size,szType,(char*)mData.contentType.strName.c_str(),senderID,
            ctnid,
            mData.ctnseq,
            db,
            timeout,
            senderDbDomainName,szCtnSvc,
            bBcastFlag);
    free(pData);

    memset(szType,0x00,sizeof(szType));
    memset(szCtnSvc,0x00,sizeof(szCtnSvc));
	memset(classify,0x00,sizeof(classify));
	
	strcpy(szType,"JPG");
	strcpy(szCtnSvc,"ALL");
	strcpy(classify,"IMG");

	sprintf(szFullFileName,"%d_%d.jpg",
			ctnid,
			mData.ctnseq
			);
	//createFile(path,classify,szYYYYMM,szFullFileName,pData,size,szSenderID);
	copyFile(path,classify,szYYYYMM,szFullFileName,"/user/neomms/CNT/IMG/bc.jpg",senderID);

	mData.ctnseq++;

	// 디비 위해 list 에 입력 
	memset(&mmsCtnTbl,0x00,sizeof(mmsCtnTbl));

	mmsCtnTbl.nCtnId = ctnid;
	sprintf(mmsCtnTbl.szCtnName,"%s/%s/%s",classify,szYYYYMM,szFullFileName);
	strcpy(mmsCtnTbl.szCtnMime,classify);
	mmsCtnTbl.nCtnSeq = mData.ctnseq;
	strcpy(mmsCtnTbl.szCtnSvc,szCtnSvc);
	strcpy(mmsCtnTbl.szFileName,szFullFileName);

	listMMSCtnTbl.push_back(mmsCtnTbl);


    while((  ret = mmsPacketSend.getMDataNext(mData)) == 0 )
    {
        pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), 
                mData.strData.length(), &size);

        memset(szType,0x00,sizeof(szType));
        memset(szCtnSvc,0x00,sizeof(szCtnSvc));

        sprintf(szType,(char*)mData.contentType.strType.c_str());
        sprintf(szCtnSvc,(char*)mData.strSvc.c_str());

        classifyMData(path,mmsid,szYYYYMM,(void*)pData,size,szType,
                (char*)mData.contentType.strName.c_str(),senderID,ctnid,
                mData.ctnseq,
                db,
                timeout,
                senderDbDomainName,szCtnSvc,
                bBcastFlag);
        free(pData);
    }

    if( bKTFTXT ) {
        string strParsingData;
        char szFullFileName[512];

        sprintf(szFullFileName,"%lld.txt",mmsid);
        
        ret = parsingKTF(strParsingData,(char*)strTxt.c_str(),strTxt.length());
        createFile(path,"TXT_KTF",szYYYYMM,szFullFileName,
                    (char*)strParsingData.c_str(),ret,
                    senderID);


    }

    return 0;
}

int CMMSFileProcess::classifyMData(char* path,long long mmsid ,char* szYYYYMM,
        void* pData, int size,char* classify,char* szFileName,char* szSenderID,int ctnid,int ctnseq,
        CKSSocket& db, 
        int timeout,
        char* senderDbDomainName,
        char* szCtnSvc,
        int bBcastFlag)
{
    int ret;
    char szFullFileName[512];
    CMMSCtnTbl mmsCtnTbl;
    CSenderDbMMSID senderDbMMSID;
    string strParsingData;
    int nParsingLength;

    if( strstr(classify,"TXT")  )
    {
        
		sprintf(szFullFileName,"%lld.txt",
                mmsid);

        fflush(stdout);

		// 2008-10-07 TXT 파일을 생성하지 않음
        //createFile(path,"TXT",szYYYYMM,szFullFileName,pData,size,szSenderID);


        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"SKT") )
        {
            ret = parsingSKT(strParsingData,pData,size);
            createFile(path,"TXT_SKT",szYYYYMM,szFullFileName,
                    (char*)strParsingData.c_str(),ret,
                    szSenderID);
        }

        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"KTF") )
        {
            strTxt = "";
            strTxt.reserve(0);

            strTxt = (char*)pData;
            bKTFTXT = true;
            /*
            ret = parsingKTF(strParsingData,pData,size);
            createFile(path,"TXT_KTF",szYYYYMM,szFullFileName,
                    (char*)strParsingData.c_str(),ret,
                    szSenderID);
                    */
        }


        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"LGT") )
        {
			// 2008-10-07 TXT_LGT 파일을 생성하지 않음
            //createFile(path,"TXT_LGT",szYYYYMM,szFullFileName,pData,size,szSenderID);
        }

        if( strstr(szCtnSvc,"ALL") || strstr(szCtnSvc,"SSN") )
        {
            createFile(path,"TXT_SSN",szYYYYMM,szFullFileName,pData,size,szSenderID);
        }

        sprintf(szTxtPath,"%s/%s",szYYYYMM,szFullFileName);

    } else {

// get ctn id
//        getCTNID2DB(db,  senderDbMMSID,timeout, senderDbDomainName);
        if( bBcastFlag ) return 0;

        sprintf(szFullFileName,"%d_%d.%s",
                ctnid,
                ctnseq,
                getExtension(szFileName)
                );

        createFile(path,classify,szYYYYMM,szFullFileName,pData,size,szSenderID);

        // 디비 위해 list 에 입력 
        memset(&mmsCtnTbl,0x00,sizeof(mmsCtnTbl));

        mmsCtnTbl.nCtnId = ctnid;
        sprintf(mmsCtnTbl.szCtnName,"%s/%s/%s",classify,szYYYYMM,szFullFileName);
        strcpy(mmsCtnTbl.szCtnMime,classify);
        mmsCtnTbl.nCtnSeq = ctnseq;
        strcpy(mmsCtnTbl.szCtnSvc,szCtnSvc);
        strcpy(mmsCtnTbl.szFileName,szFullFileName);

        listMMSCtnTbl.push_back(mmsCtnTbl);
        
    }

    return 0;
}



int CMMSFileProcess::createFile(char* szDefaultPath, char* szCtnType,char* szYYYYMM, char* szFileName,
        void* pData,int size,char* szSenderID)
{
    int ret;
    FILE* fp=NULL;
    char szFile[256];
    sprintf(szFile,"%s/%s/%s/%s",szDefaultPath,szCtnType,szYYYYMM,szFileName);
    printf("---- szFile[%s]\n",szFile);
    fflush(stdout);

    fp = fopen(szFile,"wb");
    if( fp == NULL )
    {
        char szDir[256];
        sprintf(szDir,"%s/%s/%s",szDefaultPath,szCtnType,szYYYYMM);
        mkdir(szDir,0777);
        fp = fopen(szFile,"wb");
        if( fp == NULL )
        {
            return -1;
        }
    }

    try {
        ret = fwrite(pData,size,1,fp);
    } catch (char* message) {
        sprintf(szErrorMsg,"--- file write Error [%s]",message);
        printf(szErrorMsg);
        fflush(stdout);
        fclose(fp);
        return -1;
    }
    fflush(fp);
    fclose(fp);

    return 0;
}

int CMMSFileProcess::copyFile(char* szDefaultPath, char* szCtnType,char* szYYYYMM, char* szFileName,
        char* szSrcFile,char* szSenderID)
{
    int ret;
    FILE* fp=NULL;
	FILE* sfp=NULL;

	char* tmpbuff;

    char szFile[256];
	int filesize = 0;

    sprintf(szFile,"%s/%s/%s/%s",szDefaultPath,szCtnType,szYYYYMM,szFileName);
    printf("---- copyFile szFile[%s]\n",szFile);
    fflush(stdout);
    printf("---- copyFile szSrcFile[%s]\n",szSrcFile);
    fflush(stdout);

    sfp = fopen(szSrcFile,"rb");
    if( sfp == NULL )
    {
		return -1;
    }

    fp = fopen(szFile,"wb");
    if( fp == NULL )
    {
        char szDir[256];
        sprintf(szDir,"%s/%s/%s",szDefaultPath,szCtnType,szYYYYMM);
        mkdir(szDir,0777);
        fp = fopen(szFile,"wb");
        if( fp == NULL )
        {
			fclose(sfp);
            return -1;
        }
    }
	
	filesize = getFileSize(sfp);

	printf("---- copyFile getFileSize[%d]\n",filesize);

    tmpbuff = (char *)malloc(sizeof(char)*filesize+1);
    memset(tmpbuff,0x00,filesize+1);
	ret = fread(tmpbuff, sizeof(char), sizeof(char)*filesize, sfp);

	try {
		ret = fwrite(tmpbuff,filesize,1,fp);
	} catch (char* message) {
		sprintf(szErrorMsg,"--- file write Error [%s]",message);
		printf(szErrorMsg);
		fflush(stdout);
		fclose(fp);
		fclose(sfp);
		free(tmpbuff);
		return -1;
	}
		free(tmpbuff);
	/*
	while ( !feof(sfp) ) {
		memset(tmpbuf,0x00,sizeof(tmpbuf));
		ret = fread(tmpbuf, 512, 512, sfp);

		printf("---- copyFile bytes_read[%d]\n",ret);

		try {
			ret = fwrite(tmpbuf,ret,1,fp);
		} catch (char* message) {
			sprintf(szErrorMsg,"--- file write Error [%s]",message);
			printf(szErrorMsg);
			fflush(stdout);
			fclose(fp);
			fclose(sfp);
			return -1;
		}
	}
	*/
    fflush(fp);
    fclose(fp);
	fclose(sfp);

    return 0;
}

int CMMSFileProcess::getFileSize(FILE *f)
{
    int size=0;
    
	fseek(f,0l,SEEK_END);
	size = ftell(f);
	fseek(f,0l,SEEK_SET);

	return size;
}


char* CMMSFileProcess::getExtension(char* fileName)
{
    int i;

    for(i=strlen(fileName);i>0;i--)
    {
        if( fileName[i] == '.' )
            break;
    }

    return fileName+i+1;
}

int CMMSFileProcess::getCTNID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID,int timeout, char* senderDbDomainName)
{
    CSenderDbInfoAck senderDbInfoAck;
    CDBUtil dbUtil;
    senderDbMMSID.header.type = GETCTNID;
    senderDbMMSID.header.leng = sizeof(CSenderDbMMSID) - sizeof(Header);
    senderDbMMSID.mmsid = 0;
//    memcpy(senderDbMMSID.szCid,cid,10);

    dbUtil.sendQuery(
            db,
            (void*)&senderDbMMSID,
            (void*)&senderDbInfoAck,
            timeout,
            senderDbDomainName);

    senderDbMMSID.mmsid = senderDbInfoAck.mmsid;
    senderDbMMSID.ctnid = senderDbInfoAck.ctnid;

    return 0;
}

void CMMSFileProcess::clearListMMSCtnTbl()
{
    listMMSCtnTblPosition pos,posPrev;

    pos = listMMSCtnTbl.begin();
    
    while( pos != listMMSCtnTbl.end() )
    {
        posPrev = pos++;
        // 해당 string 메모리 제거 해야되나?
        listMMSCtnTbl.erase(posPrev);
    }

    return ;
}




int CMMSFileProcess::getMMSCtnTblFirst(CMMSCtnTbl& mmsCtnTbl)
{
    m_MMSCtnTblPos = listMMSCtnTbl.begin();

    if( m_MMSCtnTblPos != listMMSCtnTbl.end() )
    {
        memset(&mmsCtnTbl,0x00,sizeof(mmsCtnTbl));

        mmsCtnTbl.nCtnId = (*m_MMSCtnTblPos).nCtnId;
        strcpy(mmsCtnTbl.szCtnName,(*m_MMSCtnTblPos).szCtnName);
        strcpy(mmsCtnTbl.szCtnMime,(*m_MMSCtnTblPos).szCtnMime);
        mmsCtnTbl.nCtnSeq = (*m_MMSCtnTblPos).nCtnSeq;
        strcpy(mmsCtnTbl.szCtnSvc,(*m_MMSCtnTblPos).szCtnSvc);
        strcpy(mmsCtnTbl.szFileName,(*m_MMSCtnTblPos).szFileName);

        return 0;
    }

    return -1;
}


int CMMSFileProcess::getMMSCtnTblNext(CMMSCtnTbl& mmsCtnTbl)
{

    m_MMSCtnTblPos++;

    if( m_MMSCtnTblPos != listMMSCtnTbl.end() )
    {
        memset(&mmsCtnTbl,0x00,sizeof(mmsCtnTbl));
        mmsCtnTbl.nCtnId = (*m_MMSCtnTblPos).nCtnId;
        strcpy(mmsCtnTbl.szCtnName,(*m_MMSCtnTblPos).szCtnName);
        strcpy(mmsCtnTbl.szCtnMime,(*m_MMSCtnTblPos).szCtnMime);
        mmsCtnTbl.nCtnSeq = (*m_MMSCtnTblPos).nCtnSeq;
        strcpy(mmsCtnTbl.szCtnSvc,(*m_MMSCtnTblPos).szCtnSvc);
        strcpy(mmsCtnTbl.szFileName,(*m_MMSCtnTblPos).szFileName);
        return 0;
    }

    return -1;
}

char* CMMSFileProcess::getTxtPath()
{
    return szTxtPath;
}


int  CMMSFileProcess::parsingKTF(string& strParsingData, void* pData, int size)
{
    char* szStt=NULL;
    string strLine;
    strParsingData = "";
    strParsingData.reserve(0);
    strParsingData = "<HTML><HEAD></HEAD><BODY>";

    CMMSCtnTbl mmsCtnTbl;
    int ret;

    ret = getMMSCtnTblFirst(mmsCtnTbl);

    while(ret == 0)
    {
        if( strstr(mmsCtnTbl.szCtnMime,"IMG") )
        {
            strParsingData += "<IMG SRC=\"cid:";
        } else if( strstr(mmsCtnTbl.szCtnMime,"SND") )
        {
            strParsingData += "<BGSound SRC=\"cid:";
        } else {
            ret = getMMSCtnTblNext(mmsCtnTbl);
            continue;
        }

        strParsingData += mmsCtnTbl.szFileName;
        strParsingData += "\"><BR>";


        ret = getMMSCtnTblNext(mmsCtnTbl);
    }

    string data = (char*)pData;
    int idx;

    while( (idx=data.find("\r") ) >= 0 )
        data.replace(idx,1,"");


    szStt = (char*)data.c_str();


    while(1)
    {
        szStt = matchString2(szStt,"\n",strLine);
        if( szStt == NULL )
            break;
        strParsingData += strLine;
        strParsingData += "<BR>";
    }

    strParsingData += "</BODY></HTML>";

    return strParsingData.length();

}




int  CMMSFileProcess::parsingSKT(string& strParsingData, void* pData, int size)
{
    char* szStt=NULL;
    string strLine;
    strParsingData = "";
    strParsingData.reserve(0);
    strParsingData = "<?xml version=\"1.0\" encoding=\"euc-kr\" ?>\r\n";
    strParsingData += "<xt xmlns=\"http://www.w3.org/1999/xhtml\">\r\n";
    strParsingData += "<head>\r\n";
    strParsingData += "</head>\r\n";
    strParsingData += "<body bgcolor=\"#E7E3E7\">\r\n";
    string data = (char*)pData;
    int idx;

    while( (idx=data.find("\r") ) >= 0 )
        data.replace(idx,1,"");

    regmatch_t match_t;
    regex_t preg;
    char szMatch[32];
    idx = regcomp(&preg,"&#[0-9]+;",REG_EXTENDED);
    if( idx != 0 )
        return idx;
    
    while(regexec(&preg,data.c_str(),1,&match_t,0) == 0 )
    {
        memset(szMatch,0x00,sizeof(szMatch));
        memcpy(szMatch,data.c_str()+match_t.rm_so ,  match_t.rm_eo- match_t.rm_so);
        while( (idx = data.find(szMatch) ) >= 0 )
            data.replace(idx,strlen(szMatch),"?");
    }

    regfree(&preg);


    while( (idx=data.find("&") ) >= 0 )
        data.replace(idx,1,"chozo99");

    while( (idx=data.find("chozo99") ) >= 0 )
        data.replace(idx,7,"&amp;");

    while( (idx=data.find(">") ) >= 0 )
        data.replace(idx,1,"&gt;");

    while( (idx=data.find("<") ) >= 0 )
        data.replace(idx,1,"&lt;");

    while( (idx=data.find("\"") ) >= 0 )
        data.replace(idx,1,"&quot;");


    szStt = (char*)data.c_str();


    while(1)
    {
        szStt = matchString2(szStt,"\n",strLine);
        if( szStt == NULL )
            break;

        strParsingData += "<div>";
        strParsingData += strLine;
        // strParsingData += "</div>\r\n<p></p>\r\n";
        strParsingData += "</div>\r\n";
    }

    strParsingData += "</body>\r\n</xt>\r\n";

    return strParsingData.length();

}


/** @brief 해당 태그부터 \n 까지 문자열을 가져온다.
 * @return 가져온 문자열의 다음 위치 포인트를 반환한다.
 */
char* CMMSFileProcess::matchString2(char* szOrg, char* szTag, string& strVal)
{
    char* szStt=szOrg;
    char* szEnd=NULL;          
    if( szOrg == NULL )
        return NULL;
    if ((szEnd=strstr(szOrg,szTag))) {
        if( (szEnd - szStt) == 0 )
        {
            strVal = "";
            strVal.reserve(0);
//            strVal = "\r\n";

        } else {
        //    return NULL;
            strVal = "";
            strVal.reserve(0);
            if( szEnd == NULL )
                strVal.insert(0,szStt);
            else
                strVal.insert(0,szStt,szEnd-szStt);
        }
//        memcpy(szVal,szStt,szEnd-szStt);       
    } else {
        if( strlen(szStt) > 0 )
        {
            szEnd = szStt + strlen(szStt);
            strVal = "";

            strVal.reserve(0);
            strVal.insert(0,szStt,szEnd-szStt);
            return szEnd;
        }

    }
    if( szStt == NULL ) return NULL;
    if( szEnd == NULL ) return NULL;
    return szEnd+1;
}





