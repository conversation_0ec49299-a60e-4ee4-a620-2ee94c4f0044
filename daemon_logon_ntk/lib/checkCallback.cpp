#include "checkCallback.h"

int CCheckCallback::loadCallbackList(CProcessInfo& processInfo, CLogonDbInfo& logonDbInfo)
{
	int ret;
	int nRecvLen;
	CKSSocket conn;
	TypeMsgGetCallback send;
	TypeMsgBindAck ack;
	memset(&send, 0x00, sizeof(send));
	memset(&ack, 0x00, sizeof(ack));
	char errorMsg[512] = {0x00,};
	
	//logPrintS(1,"get_send.pid:%d", send.pid);
	send.pid = logonDbInfo.nmPID;

	try {
		ret = conn.connectDomain(processInfo.logonDBName);
		if( ret != 0 )
		{
			set_errorMsg("LogonDB conn Error");
			return -1;
		}

		ret = conn.send((char*)&send, sizeof(send));

		if(ret != sizeof(send))
		{
			set_errorMsg("LogonDB conn send Error");
			conn.close();
			return -1;
		}

		nRecvLen = conn.recv((char*)&ack,sizeof(ack));

		if( nRecvLen == 0 )
		{
			sprintf(errorMsg, "close by Logon Domain(%s)", strerror(ret));
			set_errorMsg(errorMsg);

			conn.close();
			return -1;
		}

		if( nRecvLen < 0 )
		{
			sprintf(errorMsg, "Logon Domain Socket Recv Error(%s)", strerror(ret));
			set_errorMsg(errorMsg);

			conn.close();
			return -1;
		}

		if( memcmp(ack.szResult,"00",2) != 0 )
		{
			sprintf(errorMsg, "Logon Error ret(%.2s)", ack.szResult);
			set_errorMsg(errorMsg);

			conn.close();
			return -1;
		}

		conn.send("OK",2);

		set_callback_list.clear();
		while(nRecvLen > 0)
		{
			char buff[SOCKET_BUFF];
			memset(buff,0x00,SOCKET_BUFF);

			nRecvLen = conn.recv(buff, SOCKET_BUFF);

			if(nRecvLen <= 0)
				break;

			string callback = buff;

			set_callback_list.insert(callback);
			conn.send("OK", 2);
		}

		if( nRecvLen < 0 )
		{
			sprintf(errorMsg, "Logon Error ret(%.2s)", ack.szResult);
			set_errorMsg(errorMsg);
			conn.close();
			return -1;
		}
	}catch(...)
	{
		set_errorMsg("loadCallbackList() exception occured");
		return -1;
	}

	set_errorMsg("loadCallbacklist success");
	return 0;	
}

int CCheckCallback::findCallback(char *_callback)
{
	set<string>::iterator findItr = set_callback_list.find(_callback);	

	if(findItr != set_callback_list.end())
	{
		return 0;
	}	
	else
	{
		return -1;
	}
}

int CCheckCallback::loadDialCodeList(CProcessInfo& processInfo, CLogonDbInfo& logonDbInfo, set<string>& set_dialcode_list, char *_dial_code_type)
{
	int ret;
	int nRecvLen;
	CKSSocket conn;
	TypeMsgGetDialCode send;
	TypeMsgBindAck ack;
	memset(&send, 0x00, sizeof(send));
	memset(&ack, 0x00, sizeof(ack));
	char errorMsg[512] = {0x00,};

	sprintf(send.dial_code_type, _dial_code_type);
	try {
		ret = conn.connectDomain(processInfo.logonDBName);
		if( ret != 0 )
		{       
			set_errorMsg("LogonDB conn Error");
			return -1;
		}

		ret = conn.send((char*)&send, sizeof(send));

		if(ret != sizeof(send)) 
		{       
			set_errorMsg("LogonDB conn send Error");
			conn.close();
			return -1;
		}   

		nRecvLen = conn.recv((char*)&ack,sizeof(ack));

		if( nRecvLen == 0 )
		{       
			sprintf(errorMsg, "close by Logon Domain(%s)", strerror(ret));
			set_errorMsg(errorMsg);

			conn.close();
			return -1;
		}

		if( nRecvLen < 0 )
		{
			sprintf("Logon Domain Socket Recv Error(%s)", strerror(ret));
			set_errorMsg(errorMsg);

			conn.close();
			return -1;
		}

		if( memcmp(ack.szResult,"00",2) != 0 )
		{
			sprintf(errorMsg, "Logon Error ret(%.2s)", ack.szResult);
			set_errorMsg(errorMsg);

			conn.close();
			return -1;
		}

		conn.send("OK",2);

		while(nRecvLen > 0)
		{
			char buff[SOCKET_BUFF];
			memset(buff, 0x00, SOCKET_BUFF);

			nRecvLen = conn.recv(buff, SOCKET_BUFF);

			if(nRecvLen <= 0)
				break;

			string dialcode = buff;
			//"dial_code(%s)", dialcode.c_str());

			set_dialcode_list.insert(dialcode);
			conn.send("OK", 2);
		}

		if( nRecvLen < 0 )
		{
			sprintf(errorMsg, "Logon Domain Socket Recv Error(%s)", strerror(ret));
			set_errorMsg(errorMsg);
			conn.close();
			return -1;
		}

	}catch(...)
	{
		set_errorMsg("loadDialCodeList() exception occured");
		return -1;
	}

	return 0;
}

int CCheckCallback::loadDialCodeAll(CProcessInfo& processInfo, CLogonDbInfo& logonDbInfo)
{
    if(loadDialCodeList(processInfo, logonDbInfo, set_dialcode_list_0101, "0101") < 0)
    {
        return -1;
    }

    if(loadDialCodeList(processInfo, logonDbInfo, set_dialcode_list_0102, "0102") < 0) 
	{
        return -1;
    }

    if(loadDialCodeList(processInfo, logonDbInfo, set_dialcode_list_0103, "0103") < 0)
    {
        return -1;
    }

    if(loadDialCodeList(processInfo, logonDbInfo, set_dialcode_list_0104, "0104") < 0)
    {
        return -1;
    }

	return 0;
}

int CCheckCallback::examineCallback(string _callback)
{
	char errorMsg[512] = {0x00,};

	//자릿수 검증
	if(_callback.size() < 8 || _callback.size() > 12)
	{
		sprintf(errorMsg, "[ERR] [%s]번호 체계에 맞지 않습니다.(자리수)", _callback.c_str());
		set_errorMsg(errorMsg);
		return -1;
	}

	//휴대폰번호 및 특수 번호 체크
	set<string>::iterator findItr1 = set_dialcode_list_0101.find(_callback.substr(0, 3));

	if(findItr1 != set_dialcode_list_0101.end())
	{
		//sprintf(errorMsg, "[ERR] callbacksize[%d]", _callback.size());
		//set_errorMsg(errorMsg);

		if(_callback.size() != 10 && _callback.size() != 11)
		{
			string val = (string)*findItr1;
			sprintf(errorMsg, "[ERR] [%s]으로 시작하는 번호는 10자리 또는 11자리만 가능합니다.[%s]", 
					val.c_str(), _callback.c_str());
			set_errorMsg(errorMsg);

			return -1;
		}
		else
		{
			return 1;
		}
	}
	
	//공용서비스 번호 체크
	set<string>::iterator findItr2 = set_dialcode_list_0102.find(_callback.substr(0, 3));

	if(findItr2 != set_dialcode_list_0102.end())
	{
		//sprintf(errorMsg, "[ERR] callbacksize[%d]", _callback.size());
		//set_errorMsg(errorMsg);

		if(_callback.size() != 10 && _callback.size() != 11)
		{
			string val = (string)*findItr2;
			sprintf(errorMsg, "[ERR] [%s]으로 시작하는 번호는 10자리 또는 11자리만 가능합니다.[%s]", 
					val.c_str(), _callback.c_str());
			set_errorMsg(errorMsg);

			return -1;
		}
		else
		{
			return 1;
		}
	}

	//지역번호 체크
	set<string>::iterator itrAreaNum;
	if(_callback.substr(0, 2) == "02") //서울만 2자리라서 앞 두자리만 검사
	{
		itrAreaNum = set_dialcode_list_0103.find(_callback.substr(0, 2));
	}
	else
	{   
		itrAreaNum = set_dialcode_list_0103.find(_callback.substr(0, 3));
	}

	if(itrAreaNum != set_dialcode_list_0103.end())
	{
		return 1;	
	}

	//대표번호 체크
	set<string>::iterator findKeyItr = set_dialcode_list_0104.find(_callback.substr(0, 4));
	if(findKeyItr != set_dialcode_list_0104.end())
	{
		if(_callback.size() != 8)
		{
			sprintf(errorMsg, "[ERR] [%s]으로 시작하는 대표번호는 8자리만 가능합니다.[%s]", 
					_callback.substr(0, 4).c_str(), _callback.c_str());
			set_errorMsg(errorMsg);

			return -1;
		}
		else
		{
			return 1;
		}       
	}

	sprintf(errorMsg, "[ERR] 회신번호 체계가 유효하지 않습니다.[%s]", _callback.c_str());
	set_errorMsg(errorMsg);

	return -1;
}
