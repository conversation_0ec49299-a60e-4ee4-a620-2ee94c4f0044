# Database configuration template for daemon_logon_ntk
# Copy this file to db_config.cmake and set your values
# This file takes precedence over environment variables

# Database connection string
set(DBSTRING "NEO223")

# Database user ID
set(DBID "neontk")

# Database password
set(DBPASS "your_password_here")

# Example usage:
# 1. Copy this file: cp db_config.cmake.template db_config.cmake
# 2. Edit db_config.cmake with your actual database credentials
# 3. The CMake build will automatically use these settings

# Note: You can also use environment variables instead:
# export DBSTRING=NEO223
# export DBID=neontk
# export DBPASS=your_password