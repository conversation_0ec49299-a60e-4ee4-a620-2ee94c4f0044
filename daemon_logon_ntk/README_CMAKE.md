# daemon_logon_ntk CMake Configuration

This document describes the updated CMake configuration for the `daemon_logon_ntk` project, based on the improved patterns from `daemon_logon_ftk`.

## Prerequisites

Before building with CMake, ensure you have the following:

1. **CMake 3.16 or higher**
2. **Oracle Database client libraries** (automatically detected from multiple locations)
3. **Oracle Pro*C preprocessor** (`proc` command, automatically found)
4. **KSkyB custom libraries** installed in `$HOME/library` or built locally
5. **External dependencies** from `command_logon_ntk` project built first

## Database Configuration

The CMake configuration supports multiple ways to set database credentials:

### Option 1: Database Config File (Recommended)
```bash
# Copy template and edit
cp db_config.cmake.template db_config.cmake
# Edit db_config.cmake with your credentials
```

### Option 2: Environment Variables
```bash
export DBSTRING=NEO223
export DBID=neontk
export DBPASS=your_password
```

### Option 3: Default Values
If neither file nor environment variables are set, default values will be used.

## Oracle Configuration

The CMake configuration automatically detects Oracle installations from:
- `$ORACLE_HOME` environment variable
- `/usr/lib/oracle/21/client64`
- `/usr/lib/oracle/19/client64`
- `/usr/lib/oracle/18/client64`
- `/usr/lib/oracle/12.2/client64`

## Build Instructions

### 1. Create build directory

```bash
cd daemon_logon_ntk
mkdir build
cd build
```

### 2. Configure with CMake

```bash
cmake ..
```

### 3. Build all executables

```bash
# Build standard executables
make logonSession logonDB admin monitorProcess adminProcess

# Build Pro*C executables (requires Oracle Pro*C)
make senderNtalkProDB reportMMSProcDB

# Build all executables at once
make all_executables
```

## Executables Built

The CMake configuration builds the following executables:

### Standard C++ Executables
- `logonSession` - Session management daemon
- `logonDB` - Database connection daemon  
- `admin` - Administration utility
- `monitorProcess` - Process monitoring daemon
- `adminProcess` - Administrative process daemon

### Oracle Pro*C Executables
- `senderNtalkProDB` - NTalk sender with database integration
- `reportMMSProcDB` - MMS report processing with database

## Directory Structure

```
daemon_logon_ntk/
├── CMakeLists.txt          # Main CMake configuration
├── README_CMAKE.md         # This documentation
├── inc/                    # Header files
├── lib/                    # Library source files
├── src/                    # Main source files
├── bin/                    # Built executables (created by CMake)
├── obj/                    # Object files and Pro*C intermediates (created by CMake)
└── mak/                    # Original makefile (for reference)
```

## Oracle Pro*C Integration

The CMake configuration handles Oracle Pro*C preprocessing automatically:

1. **Source files** with embedded SQL are copied to `.pc` files
2. **Pro*C preprocessor** converts `.pc` files to `.cpp` files
3. **Generated C++** files are compiled to object files
4. **Custom linking** combines Pro*C objects with other components

### Pro*C Configuration
- Mode: ORACLE
- DBMS: V7
- Code: CPP (C++)
- SQL Check: FULL
- Database connection: neontk/kskybDB0955**@NEO223

## Dependencies

### External Libraries
- `ksbase64`, `kssocket`, `ksconfig`, `ksthread` (KSkyB libraries)
- `clntsh`, `orapp` (Oracle libraries)
- `pthread`, `crypto`, `nsl` (System libraries)

### External Object Files
- `command_logon_ntk/obj/sms_ctrlsub++.o` (must be built first)

## Troubleshooting

### Common Issues

1. **Oracle Pro*C not found**
   ```
   Solution: Ensure ORACLE_HOME is set and proc is in PATH
   ```

2. **External object file missing**
   ```
   Solution: Build command_logon_ntk project first
   ```

3. **KSkyB libraries not found**
   ```
   Solution: Ensure libraries are installed in $HOME/library
   ```

4. **Database connection failed during Pro*C**
   ```
   Solution: Check database credentials and connectivity
   ```

### Clean Build

To clean all generated files:

```bash
make clean-all
```

This removes:
- Object files in `obj/` directory
- Temporary Pro*C files (`tp*`)
- Generated C++ files from Pro*C

## CLion Integration

The CMake configuration includes CLion-specific features:

- **CLION_BUILD** definition when building in CLion
- **Proper include paths** for code completion
- **Custom targets** visible in CLion build configurations

## Comparison with Original Makefile

The CMake configuration provides the same functionality as the original makefile with these improvements:

- **Cross-platform** build support
- **Better dependency** management
- **IDE integration** (CLion, VS Code, etc.)
- **Parallel builds** support
- **Cleaner configuration** syntax

## Notes

- The original makefile is preserved in `mak/makefile` for reference
- Database credentials are embedded in Pro*C preprocessing (consider externalizing)
- Some executables are commented out in the original makefile and not included here
- The configuration assumes a specific directory structure and dependencies
