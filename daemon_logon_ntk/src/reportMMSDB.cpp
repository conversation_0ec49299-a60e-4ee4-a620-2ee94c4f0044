#include "reportDB.h"
#include <queue>
#include <list>
#include <semaphore.h> 

EXEC SQL BEGIN DECLARE SECTION;
#define MAX_DB_CONNECTION 40
EXEC SQL END DECLARE SECTION;

void Init_Oracle(sql_context ctx);
void* doService(void* param);
int getReportDB(sql_context pDB,struct sqlca sqlca, char* buff,CReportDbInfo& reportDbInfo);
int setReportDB(sql_context pDB,struct sqlca sqlca, char* buff,CReportDbInfo& reportDbInfo);
void* doDBError(void* param);
int errorDBprocess(void* pDB);
int offerInfo(CKSSocket& newSockfd);

queue<void*, list<void*> > dbConnQ;
std::list <CThreadInfo*> listThreadHandle;
typedef std::list <CThreadInfo*>::iterator listPosition;

sem_t m_sem;

int main(int argc, char* argv[])
{
	int ret;
	int i;
	int nThreadCount = 0;
	int hNewSocket;
	
	char logMsg[SOCKET_BUFF];
	char buff[SOCKET_BUFF];
	
	struct timeval outtime;

	CKSSocket svrSockfd;
	CKSSocket newSockfd;
	CKSThread ksthread;
	CThreadInfo* pThreadInfo;
	TypeMsgBindSnd* pLogonData;
	CKSConfig conf;

	listPosition pos;
	listPosition posPrev;

	EXEC SQL BEGIN DECLARE SECTION;
		sql_context db[MAX_DB_CONNECTION];
	EXEC SQL END DECLARE SECTION;

	if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0)
	{
		perror("ml_sub_init Error.");
		ml_sub_end();
		exit(1);
	}

	ret = configParse(argv[1]);
	if( ret != 0 )
	{
		exit(1);
	}

	log_history(0,0,"[INF] conf reportDBName - [%s]",gConf.reportDBName);

	sprintf(logMsg,"[INF] start [%s]",PROCESS_NAME);
	monitoring(logMsg,0,0);

	Init_Server();

	EXEC SQL ENABLE THREADS;
	
	for(i=0;i<MAX_DB_CONNECTION;i++)
	{
		EXEC SQL CONTEXT ALLOCATE :db[i];

		Init_Oracle(db[i]);
		if( sqlca.sqlcode !=0 )
		{
			monitoring("[ERR] db connect failed",0,errno);
			ml_sub_end();
			return -1;
		}

		log_history(0,0,"[INF] db sql_context - value [%x]",db[i]);
		dbConnQ.push(db[i]);
	}

	sem_init(&m_sem,0,1);

	ret = svrSockfd.createDomainNon(gConf.reportDBName);
	
	if( ret !=0 )
	{
		log_history(0,0,"[ERR] socket_domain create failed - conf.reportDBName[%s]",strerror(errno),gConf.reportDBName);
		goto END;
	}

    

	while(activeProcess)
	{
		wait_a_moment(1000);
		pos = listThreadHandle.begin();
		nThreadCount = 0;
		while( pos != listThreadHandle.end() )
		{
			nThreadCount++;
			posPrev = pos++;

			if( (*posPrev)->tid <= 0 )
			{
				log_history(0,0,"[INF] thread tid - [%d]",(*posPrev)->tid);
				listThreadHandle.erase(posPrev);
				continue;
			}

			ret =  pthread_kill((*posPrev)->tid,0);

			switch (ret)
			{
				case 0 : 
					break;
				case ESRCH:
					pthread_join((*posPrev)->tid,NULL);
					listThreadHandle.erase(posPrev);
					delete (CThreadInfo*)(*posPrev);
					break;
				case EINVAL:
					pthread_join((*posPrev)->tid,NULL);
					listThreadHandle.erase(posPrev);
					delete (CThreadInfo*)(*posPrev);
					break;
				default:
					log_history(0,0,"[ERR] thread check failed - ret[%d]errno[%s]",ret,strerror(errno));
					pthread_join((*posPrev)->tid,NULL);
					listThreadHandle.erase(posPrev);
					delete (CThreadInfo*)(*posPrev);
					break;
				}
		}


		hNewSocket = svrSockfd.accept();
		if( hNewSocket <= 0 )
		{
			continue;
		}
		/* new connection  */
		pThreadInfo = NULL;
		pThreadInfo = new CThreadInfo;
		if( pThreadInfo == NULL )
		{
			log_history(0,0,"[ERR] thread threadInfo object create failed - errno[%s]",strerror(errno));
			close(hNewSocket);
			continue;
		}

		pThreadInfo->sock = hNewSocket;
		
		ret = ksthread.create(&(pThreadInfo->tid),NULL,doService,(void*)pThreadInfo);
		if( ret != 0 )
		{
			log_history(0,0,"[ERR] thread create failed - errno[%s]",strerror(errno));
			close(pThreadInfo->sock);
			delete pThreadInfo;
			continue;
		}
		listThreadHandle.push_back( pThreadInfo);

	}

END:
	svrSockfd.close();
	pos = listThreadHandle.begin();
	nThreadCount = 0;
	while( pos != listThreadHandle.end() )
	{
		nThreadCount++;
		posPrev = pos++;
		ret =  pthread_kill((*posPrev)->tid,0);

		switch (ret) {
			case 0 : /* thread is alive */     
				log_history(0,0,"[INF] thread closing - listThreadHandle[%d]",(pthread_t)(*posPrev));
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
			case ESRCH:
			case EINVAL:
				/* close thread */
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
			default:
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
		}
	}

	sem_destroy(&m_sem);
	EXEC SQL BEGIN DECLARE SECTION;
		sql_context pDB;
	EXEC SQL END DECLARE SECTION;

	sleep(2);//?
	while( dbConnQ.size()>0 )
	{
		pDB = dbConnQ.front();
		if( pDB == NULL )
		{
			break;
		}

		EXEC SQL CONTEXT USE :pDB;
		EXEC SQL COMMIT WORK RELEASE;
		EXEC SQL CONTEXT FREE :pDB;

		dbConnQ.pop();
	}
    
	sprintf(logMsg,"[INF] close [%s]",PROCESS_NAME);
	monitoring(logMsg,0,0);
	ml_sub_end();

	return 0;
}


void* doService(void* param)
{
	int ret;
	CKSSocket newSockfd;
	char buff[SOCKET_BUFF];
	CThreadInfo* info = (CThreadInfo*)param;
	CReportDbInfo reportDbInfo;
	CReportDbInfo* pReportDbInfo;

	void* pDB;
	struct sqlca sqlca;

	newSockfd.attach(info->sock);
	memset(buff	,0x00	,sizeof(buff));//CCL(buff);

	ret = newSockfd.rcvmsg(buff);
	if( ret == 0 ) 
	{
		newSockfd.close();
		log_history(0,0,"[ERR] socket_domain reportDB - time out [3]sec");
		
		return NULL;
	}
	if( ret < 0 ) 
	{
		newSockfd.close();
		log_history(0,0,"[ERR] socket_domain reportDB - recv failed ");
        
		return NULL;
	}
	if( memcmp(buff,"getInfo",7) == 0 )
	{
		offerInfo(newSockfd);
		newSockfd.close();
		
		return NULL;
	}

/*    log_history(0,0,"doService ... start");
 */
REGETDB:
	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{ 
			newSockfd.close();
			log_history(0,0,"[ERR] semaphore wait failed");
			
			return NULL;
		}
	}

	if( dbConnQ.size() > 0 )
	{
		pDB = dbConnQ.front();
		if( pDB != NULL ) 
		{
			//log_history(0,0,"pop NOT NULL pDB[%x] [%d]", pDB,dbConnQ.size()); 
			dbConnQ.pop();
		}
	}
	else
	{
		pDB = NULL;
	}

	if( sem_post(&m_sem) == -1 )
	{
		newSockfd.close();
		log_history(0,0,"[ERR] semaphore clear failed - [%s]",strerror(errno));
		
		return NULL;
	}

	if( pDB == NULL )
	{
		ret = 0;
		log_history(0,0,"NODATA[%s]",strerror(errno));
		goto NODATA;
	}

	memset(&reportDbInfo	,0x00	,sizeof(reportDbInfo));

	alarm(15);
	pReportDbInfo = (CReportDbInfo*)buff;
	
	switch(atoi(pReportDbInfo->header.msgType))
	{
		case 1: /* 전송결과 요청 */
			ret = getReportDB(pDB,sqlca,buff,reportDbInfo);
			if( ret < 0 )
			{
				newSockfd.close();
				log_history(0,0,"getReportDB Error");
				ret = errorDBprocess(pDB);
				alarm(0);
			
				return NULL;
			}

			break;
		case 3: /* 레포트 재입력 */
			/*
			ret = setReportDB(pDB,sqlca,buff,reportDbInfo);
			if( ret < 0 )
			{
				newSockfd.close();
				log_history(0,0,"setReportDB Error");
				ret = errorDBprocess(pDB);
				alarm(0);
				
				return NULL;
			}
			log_history(0,0,"setReportDB Succ");
			*/
			break;
		default:
			ret = -1;
			log_history(0,0,"[ERR] packet msg type failed - reportDbInfo header msgType[%s]",pReportDbInfo->header.msgType);
			break;
	}
	alarm(0);

	if( ret < 0 )
	{
		newSockfd.close();
		while( sem_wait(&m_sem) == -1 )
		{
			if(errno != EINTR )
			{ 
				newSockfd.close();
				log_history(0,0,"[ERR] semaphore wait failed");

				return NULL;
			}
		}
		dbConnQ.push(pDB);

		if( sem_post(&m_sem) == -1 )
		{
			newSockfd.close();
			log_history(0,0,"[ERR] semaphore clear failed - errno[%s]",strerror(errno));
			
			return NULL;
		}
		return NULL;
	}

	if( ret == 0 )
	{
		//memcpy(reportDbInfo.szResCode,"99",2);
		strcpy(reportDbInfo.szResCode,"99");
	}

	ret = newSockfd.send((char*)&reportDbInfo,sizeof(reportDbInfo));
	
	if( ret == sizeof(reportDbInfo))
	{
		/* commit */
		EXEC SQL CONTEXT USE :pDB;
		EXEC SQL COMMIT;
	}
	else 
	{
		log_history(0,0,"[ERR] socket_domian send failed - ret[%d]errno[%s]rescode[%S]",ret,strerror(errno),reportDbInfo.szResCode);
		
		//if( memcmp(reportDbInfo.szResCode,"99",2) != 0 &&  memcmp(reportDbInfo.szResCode,"98",2) != 0 )
		if( memcmp(reportDbInfo.szResCode,"99", strlen(reportDbInfo.szResCode)) != 0 &&  
			memcmp(reportDbInfo.szResCode,"98", strlen(reportDbInfo.szResCode)) != 0 )
		{
			/* rollback */
			EXEC SQL CONTEXT USE :pDB;
			EXEC SQL ROLLBACK;
			log_history(0,0,"[INF] db rollback msgid[%lld]rescode[%s]",reportDbInfo.nMsgId,reportDbInfo.szResCode);
		}
	}


	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{ 
			newSockfd.close();
			log_history(0,0,"[ERR] semaphore wait failed");
			
			return NULL;
		}
	}

	dbConnQ.push(pDB);
	
	if( sem_post(&m_sem) == -1 )
	{
		newSockfd.close();
		log_history(0,0,"[ERR] semaphore clear failed - errno[%s]",strerror(errno));
		
		return NULL;
	}

	newSockfd.close();

	return NULL;

NODATA:
	if( ret == 0 )
	{
		//memcpy(reportDbInfo.szResCode,"99",2);
		strcpy(reportDbInfo.szResCode,"99");
	}
	
	ret = newSockfd.send((char*)&reportDbInfo,sizeof(reportDbInfo));
	if( ret != sizeof(reportDbInfo))
	{
		log_history(0,0,"[ERR] socket_domain send NODATA - ret[%d]errno[%s]",ret,strerror(errno));
	}

	newSockfd.close();

	return NULL;
}

int getReportDB(sql_context pDB,struct sqlca sqlca, char* buff ,CReportDbInfo& reportDbInfo)
{
	EXEC SQL BEGIN DECLARE SECTION;

		char szPtnsn[32+1]; //20180829 szPtnsn 20 -> 32
		char szResCode[16+1];
		int  nTelcoId =-1;
		char szResvData[512];
		char szEndTelco[8];
		long long  nMsgId=-1;
		char szMsgId[14+1];
		char szRptDate[16];
		int  nJobCode=-1;
		int  nSqlCode=-1;
		
		int  nCnt = -1;
		char szSqlErrorMsg[1024];
			
		char szAppName[16];
		char szSerial[16+1];
		char szDstadr[16+1];
		char szCid[12+1]; /*  ptnid&jobcode 대체 */
		
		int nPtnId = -1;

	EXEC SQL END DECLARE SECTION;

	CReportDbInfo* pReportDbInfo = (CReportDbInfo*)buff;

	memset(szPtnsn       ,0x00, sizeof(szPtnsn      ));   //CCL(szPtnsn);      
	memset(szResCode     ,0x00, sizeof(szResCode    ));   //CCL(szResCode);    
	memset(szResvData    ,0x00, sizeof(szResvData   ));   //CCL(szResvData);   
	memset(szDstadr      ,0x00, sizeof(szDstadr     ));   //CCL(szDstadr);     
	memset(szEndTelco    ,0x00, sizeof(szEndTelco   ));   //CCL(szEndTelco);   
	memset(szRptDate     ,0x00, sizeof(szRptDate    ));   //CCL(szRptDate);    
	memset(szSqlErrorMsg ,0x00, sizeof(szSqlErrorMsg));   //CCL(szSqlErrorMsg);
	memset(szAppName     ,0x00, sizeof(szAppName    ));   //CCL(szAppName);    
	memset(szCid         ,0x00, sizeof(szCid        ));   //CCL(szCid);        
    memset(szMsgId       ,0x00, sizeof(szMsgId      ));

	strcpy(szAppName,pReportDbInfo->szResvData);
	memcpy(szResCode,"Init",4);
    
#ifdef DEBUG
	log_history(0,0,"appname[%s][%x]",szAppName,pDB);
#endif

	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
			proc_get_mms_report(in_appname=>:szAppName
                					,ot_ptn_sn=>:szPtnsn
                					,ot_res_code=>:szResCode
                					,ot_telco_id=>:nTelcoId
                					,ot_end_telco=>:szEndTelco
                					,ot_msg_id=>:szMsgId
                					,ot_resv_data=>:szResvData
                					,ot_dstaddr=>:szDstadr
                					,ot_rpt_telco_date=>:szRptDate
                					,ot_cid=>:szCid 
                					,ot_cnt=>:nCnt
                					,ot_sqlcode=>:nSqlCode
                					,ot_sqlmsg=>:szSqlErrorMsg
                					); 
		END;
	END-EXEC;
/*              ot_ptn_id=>:nPtnId,
                ot_job_code=>:nJobCode,
*/

	if( memcmp(szResCode,"Init",4) == 0 )
	{
		log_history(0,0,"[ERR] db call proc_get_report_time - ptnsn[%s] cid[%s] sqlcode[%d] sqlmsg[%s][%s]"
                ,szPtnsn
                ,szCid
                ,sqlca.sqlcode
                ,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc))
                ,szAppName
                );
		return -1;
	}



	/* 
	 * NO DATA FOUND 일 경우 SKIP
	 * szResCode '999'일경우도 있어 수정
	 * szResCode에 space 포함되어있어 trim 위치 올림
	 */
	trim(szResCode	,sizeof(szResCode)	);// LSY 20131115 결과코드값 trim 추가
	//if( memcmp(szResCode,"99",2) == 0 || memcmp(szResCode,"98",2) == 0 )
	if( memcmp(szResCode,"99", strlen(szResCode)) == 0 || memcmp(szResCode,"98", strlen(szResCode)) == 0 )
	{
		return 0;
	}
	
	trim(szPtnsn	,sizeof(szPtnsn)	);
	trim(szResvData	,sizeof(szResvData)	);
	trim(szDstadr	,sizeof(szDstadr)	);
	trim(szEndTelco	,sizeof(szEndTelco)	);
	trim(szRptDate	,sizeof(szRptDate)	);
	
	strcpy(reportDbInfo.header.msgType,"2");

	memcpy(reportDbInfo.szPtnsn		,szPtnsn		,strlen(szPtnsn));
	memcpy(reportDbInfo.szResCode	,szResCode	,strlen(szResCode));
	memcpy(reportDbInfo.szAppName	,szAppName	,strlen(szAppName));
	
	reportDbInfo.nTelcoId 	= nTelcoId;
	
	memcpy(reportDbInfo.szResvData	,szResvData	,strlen(szResvData));
	memcpy(reportDbInfo.szDstAdr	,szDstadr	,strlen(szDstadr));
	memcpy(reportDbInfo.szEndTelco	,szEndTelco	,strlen(szEndTelco));
	
	reportDbInfo.nMsgId 	= atoll(szMsgId);
	
	memcpy(reportDbInfo.szRptDate	,szRptDate	,strlen(szRptDate));
	
	reportDbInfo.nCnt 		= nCnt;
	reportDbInfo.nSqlCode 	= nSqlCode;
	
	memcpy(reportDbInfo.szCid		,szCid		,strlen(szCid));
	
	/*
	reportDbInfo.nJobCode = nJobCode;
	reportDbInfo.nPtnId = nPtnId;
	*/
	
	return 1;
}



void Init_Oracle(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;

		VARCHAR Username[10];
		VARCHAR Password[10];
		VARCHAR dbstring[10];

	EXEC SQL END DECLARE SECTION;
	
	strcpy((char*)Username.arr	,gConf.dbID);
	strcpy((char*)Password.arr	,gConf.dbPASS);
	strcpy((char*)dbstring.arr	,gConf.dbSID);
	
	Username.len = strlen((char*)Username.arr);
	Password.len = strlen((char*)Password.arr);
	dbstring.len = strlen((char*)dbstring.arr);
	
	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring ;
}


int errorDBprocess(void* pDB)
{
	CKSThread dbErr;
	int ret;
	pthread_t tid;
	
	ret = dbErr.create(&(tid),NULL,doDBError,(void*)pDB);
	if( ret != 0 )
	{
	    log_history(0,0,"[ERR] thread errorDBprocess create failed - errno[%s]dbQueSize[%d]",
	            strerror(errno),
	            dbConnQ.size());
	}
	
	return -1;
}


void* doDBError(void* param)
{

	pthread_detach(pthread_self()); 
	struct sqlca sqlca;
	EXEC SQL BEGIN DECLARE SECTION;
		sql_context pDB = (sql_context)param;
	EXEC SQL END DECLARE SECTION;

DBERRCONN:
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL COMMIT WORK RELEASE;
	EXEC SQL CONTEXT FREE :pDB;

	if( activeProcess == false )
	{
		return NULL;
	}
	
	EXEC SQL CONTEXT ALLOCATE :pDB;
	
	Init_Oracle(pDB);
	
	if( sqlca.sqlcode !=0 )
	{
		monitoring("[ERR] db connection failed",0,errno);
		wait_a_moment(900000);
		goto DBERRCONN;
	}
	
	log_history(0,0,"[INF] db object create - sql_context[%x]",pDB);
	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{ 
			log_history(0,0,"[ERR] semaphore wait failed");
			wait_a_moment(900000);
			goto DBERRCONN;
		}
	}
	/* 크리티컬 섹션 */
	dbConnQ.push(pDB);
	
	if( sem_post(&m_sem) == -1 )
	{
		log_history(0,0,"[ERR] semaphore clear failed - errno[%s]pdb[%x]",strerror(errno),pDB);
		return NULL;
	}
	
	log_history(0,0,"[INF] db doDBError - queueSize[%d]",dbConnQ.size());
	
	return NULL;
}

int offerInfo(CKSSocket& newSockfd)
{
	char szQueSize[4];
	memset(szQueSize	,0x00	,sizeof(szQueSize));//CCL(szQueSize);
	sprintf(szQueSize	,"%d"	,dbConnQ.size());

	newSockfd.send(szQueSize,strlen(szQueSize));
	
	return 0;
}

int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	CKSConfig conf;
	//memset(&gConf,0x00,sizeof(gConf));

	// read mert conf
	if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}

	conf.strncpy2(gConf.reportDBName	,conf.FetchEntry("domain.reportDB")	,64);
	if( gConf.reportDBName == NULL )
	{
		strcpy(gConf.reportDBName,"");
	}

	conf.strncpy2(gConf.dbID			,conf.FetchEntry("db.id")			,16);
	if( gConf.dbID == NULL )
	{
		strcpy(gConf.dbID,"");
	}

	conf.strncpy2(gConf.dbPASS			,conf.FetchEntry("db.pass")			,16);
	if( gConf.dbPASS == NULL )
	{
		strcpy(gConf.dbPASS,"");
	}

	conf.strncpy2(gConf.dbSID			,conf.FetchEntry("db.sid")			,16);
	if( gConf.dbSID == NULL )
	{
		strcpy(gConf.dbSID,"");
	}
    return 0;
}
