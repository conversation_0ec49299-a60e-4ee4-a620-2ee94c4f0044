#include "senderMMSDB.h"

EXEC SQL BEGIN DECLARE SECTION;
#define MAX_DB_CONNECTION 20
EXEC SQL END DECLARE SECTION;

queue<void*, list<void*> > dbConnQ;

std::list <CThreadInfo*> listThreadHandle;
typedef std::list <CThreadInfo*>::iterator listPosition;

/*
 * queue<pthread_t, list<pthread_t> > threadQ;
 */

sem_t m_sem;

void logfunc(const char *s) 
{
    log_history(0,0,"[%s]",s);
}

int main(int argc, char* argv[])
{
	int ret;
	int i;
	int hNewSocket;
	
	char logMsg[SOCKET_BUFF];
	char buff[SOCKET_BUFF_TALK];
	
	struct timeval outtime;
	
	CKSConfig conf;
	CKSSocket svrSockfd;
	CKSSocket newSockfd;
	CKSThread ksthread;
	
	CThreadInfo* pThreadInfo;
	TypeMsgBindSnd* pLogonData;

	int nThreadCount = 0;
	listPosition pos;
	listPosition posPrev;
	


	EXEC SQL BEGIN DECLARE SECTION;
		sql_context db[MAX_DB_CONNECTION];
	EXEC SQL END DECLARE SECTION;

	if ( ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0 ) 
	{
		perror("ml_sub_init Error.");
		ml_sub_end();
		exit(1);
	}
    
    //PD1. CONFIG PARSING 
	ret = configParse(argv[1]);
	if( ret != 0 )
	{
		exit(1);
	}
    
	log_history(0,0,"[INF] PD1. CONFIG PARSING conf senderDBName - [%s]",gConf.senderDBName);
    
    //PD2. 프로세스 시작
	sprintf(logMsg,"[INF] PD2. 프로세스 시작 start [%s]",PROCESS_NAME);
	monitoring(logMsg,0,0);

	Init_Server();
    
    //PD3. 쓰레드 사용
	EXEC SQL ENABLE THREADS;
    
	for(i=0;i<MAX_DB_CONNECTION;i++)
	{
		EXEC SQL CONTEXT ALLOCATE :db[i];

		Init_Oracle(db[i]);

		if( sqlca.sqlcode !=0 )
		{
			sprintf(logMsg,"[ERR] PD3. 쓰레드 사용 db connect failed [%d]",sqlca.sqlcode);
			monitoring(logMsg,0,errno);
			ml_sub_end();
    
			return -1;
		}
    
		log_history(0,0,"[INF] PD3. 쓰레드 사용 db sql_context - value[%x]",db[i]);
		dbConnQ.push(db[i]);
	}
    
	sem_init(&m_sem,0,1);
    
    //PD4. 도메인 생성
	ret = svrSockfd.createDomainNon(gConf.senderDBName);
    
	if( ret !=0 ) 
	{
		log_history(0,0,"[ERR] D4. 도메인 생성 socket_domain create failed - errno[%s]",strerror(errno),gConf.senderDBName);
		goto ENDMAIN;
	}
    
    //PD5. 쓰레드 처리
	while(activeProcess)
	{
		wait_a_moment(100);
		pos = listThreadHandle.begin();
		nThreadCount = 0;
    
		while( pos != listThreadHandle.end() )
		{
			nThreadCount++;
			posPrev = pos++;
    
			if( (*posPrev)->tid <= 0 )
			{
				log_history(0,0,"[INF] D5. 쓰레드 처리 thread - tid [%d]",(*posPrev)->tid);
				listThreadHandle.erase(posPrev);
				continue;
			}
    
			ret =  pthread_kill((*posPrev)->tid,0);
    
			switch (ret) {
				case 0 : /* thread is alive */
					break;
				case ESRCH:
				case EINVAL:
					pthread_join((*posPrev)->tid,NULL);
					listThreadHandle.erase(posPrev);
					delete (CThreadInfo*)(*posPrev);
					break;
				default:
					pthread_join((*posPrev)->tid,NULL);
					listThreadHandle.erase(posPrev);
					delete (CThreadInfo*)(*posPrev);
					break;
			}
		}
    
        //PD6. 소켓 연결 수신
		hNewSocket = svrSockfd.accept();
    
		if( hNewSocket <= 0 )
		{
			continue;
		}
		pThreadInfo = NULL;
		pThreadInfo = new CThreadInfo;
		
		//PD7. 쓰레드 존재여부 확인
		if( pThreadInfo == NULL )
		{
			log_history(0,0,"[ERR] PD7. thread object create failed - [%s]", strerror(errno));
			close(hNewSocket);
			continue;
		}

		pThreadInfo->sock = hNewSocket;
        
        //PD8. 쓰레드 서브루틴 실행
		ret = ksthread.create(&(pThreadInfo->tid),NULL,doService,(void*)pThreadInfo);
		if( ret != 0 )
		{
			log_history(0,0,"[ERR] PD8. 쓰레드로 서브루틴 실행 thread create failed - [%s]", strerror(errno));
			close(pThreadInfo->sock);
			delete pThreadInfo;
			continue;
		}
		
        //PD20. 쓰레드 리스트 값 세팅
		listThreadHandle.push_back( pThreadInfo);

	}

//PD9. 도메인 생성 에러시 자원 RELEASE
ENDMAIN:

    //PD9-1. 소켓 종료
	svrSockfd.close();

    //PD9-1. 쓰레드 종료
	pos = listThreadHandle.begin();
	nThreadCount = 0;
	while( pos != listThreadHandle.end() )
	{
		nThreadCount++;
		
		
		posPrev = pos++;
    
		ret =  pthread_kill((*posPrev)->tid,0);

		switch (ret) 
		{
			case 0 : /* thread is alive */
				log_history(0,0,"[INF] PD9-1. 쓰레드 종료 thread closing - listThreadHandle[%d]",(pthread_t)(*posPrev));
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
			case ESRCH:
			case EINVAL:
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
			default:
				pthread_join((*posPrev)->tid,NULL);
				listThreadHandle.erase(posPrev);
				delete (CThreadInfo*)(*posPrev);
				break;
		}
	}
	
	sem_destroy(&m_sem);

    //PD9-3. CONTEXT FREE
	EXEC SQL BEGIN DECLARE SECTION;
    
		sql_context pDB;
    
	EXEC SQL END DECLARE SECTION;

	sleep(2);
    
	while( dbConnQ.size()>0 )
	{
		pDB = dbConnQ.front();

		if( pDB == NULL )
		{
			break;
		}

		EXEC SQL CONTEXT USE :pDB;
		EXEC SQL COMMIT WORK RELEASE;
		EXEC SQL CONTEXT FREE :pDB;
		dbConnQ.pop();
	}
    
    //PD9-4. 프로세스 종료
	sprintf(logMsg,"[INF] PD9-1. 프로세스 종료 close [%s]",PROCESS_NAME);
	monitoring(logMsg,0,0);

	ml_sub_end();

	return 0;
}


void* doService(void* param)
{
	int ret;
	void* pDB;
	bool bFirst=true;
	char buff[SOCKET_BUFF_TALK_V3];
	struct sqlca sqlca;
	
	CKSSocket newSockfd;
	CThreadInfo* info = (CThreadInfo*)param;
	CSenderDbInfoAck ack;
	
	time_t ThisT,LastT;

    /* classify */
    //PD9. 구조체 선언
	//CSenderDbMMSID* pSenderDbMMSID ;
	CSenderDbMMSTBL* pSenderDbMMSTBL ;
	CSenderDbMMSMSG_TALK* pSenderDbMMSMSG ;
	//CSenderDbMMSCTNTBL* pSenderDbMMSCTNTBL;
	//CSenderDbMMSRPTTBL* pSenderDbMMSRPTTBL; 사용X
	Header* pHeader ;
	CSenderDbMMSSEQ* pSenderDbMMSSEQ ;
	CSenderDbMMSRPTQUE* pSenderDbMMSRPTQUE;

	int type ;
	char test[10];
	

	memset((char*)&ack,0x00,sizeof(ack));

    //PD10. socket attach
	newSockfd.attach(info->sock);
	memset(buff, 0x00, sizeof(buff)); 
	
	//PD11. 소켓 RECEIVE 
	ret = newSockfd.rcvmsg(buff);
	if( ret == 0 ) 
	{
		log_history(0,0,"[ERR] PD11. 소켓 RECEIVE socket_domain time out");
		goto ENDDOTHREAD;
	}

	if( ret < 0 ) 
	{
		log_history(0,0,"[ERR] PD11. 소켓 RECEIVE socket_domain recv failed");
		goto ENDDOTHREAD;
	}
//#ifdef DEBUG
//    viewPack(buff,ret);
//#endif

    //PD12. socket_domain send
	if( memcmp(buff,"getInfo",7) == 0 )
	{
		ret = offerInfo(newSockfd);
		// 20140217 add if
		if( ret < 0 )
		{
			log_history(0,0,"[ERR] PD12. socket_domain send failed ret [%d]",ret);
		}
		goto ENDDOTHREAD;
	}
	
	/* classify */
	//PD13. 각 구조체에 값 세팅
	//pSenderDbMMSID = (CSenderDbMMSID*)buff;
	pSenderDbMMSSEQ = (CSenderDbMMSSEQ*)buff;
	pSenderDbMMSTBL = (CSenderDbMMSTBL*)buff;
	pSenderDbMMSMSG = (CSenderDbMMSMSG_TALK*)buff;
	pSenderDbMMSRPTQUE = (CSenderDbMMSRPTQUE*)buff;
	//pSenderDbMMSCTNTBL = (CSenderDbMMSCTNTBL*)buff;  사용X
	//pSenderDbMMSRPTTBL = (CSenderDbMMSRPTTBL*)buff;  사용X
	pHeader = (Header*)buff;
	type = pHeader->type;
	time(&LastT);
	//int type = pSenderDbMMSID->type;
	//int type = (int)buff;
	
	//log_history(0,0,"[DBG] PD13. 각 구조체에 값 세팅 doService MMSID[%lld]",pSenderDbMMSID->mmsid);
	

REGETDB:
	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{
			log_history(0,0,"[ERR] semaphore wait failed");
			goto ENDDOTHREAD;
		}
    
	}
	/*   크리티컬 섹션
       값을 가져올때까지 반복하기(세마포어 풀고)  timeout 5 sec
  */
  
	if( dbConnQ.size() > 0 )
	{
		pDB = dbConnQ.front();
		if( pDB != NULL )
		{
			//log_history(0,0,"pop NOT NULL pDB[%x] [%d]", pDB,dbConnQ.size());
			dbConnQ.pop();
		}
	} 
	else 
	{
		pDB = NULL;
	}

	if( sem_post(&m_sem) == -1 )
	{
		log_history(0,0,"[ERR] semaphore clear failed [%s]",strerror(errno));
		goto ENDDOTHREAD;
	}

	if( pDB == NULL ) 
	{
		time(&ThisT);
		if( (ret = (int)difftime(ThisT,LastT)) > gConf.dbFindTimeOut )
		{
			log_history(0,0,"[ERR] db handle time out - [%d]sec",gConf.dbFindTimeOut);
			goto ENDDOTHREAD;
		}
		if( ret > 2 && bFirst )
		{
			bFirst = false;
			log_history(0,0,"[INF] timeout [%d]sec",ret);
		}
		wait_a_moment(1000);
		goto REGETDB;
	}

	/* DB 작업 : pDB */
	/*  log_history(0,0,"pDB [%x]",pDB); */
	alarm(15);
	switch( type ) {
		case GETMMSSEQ: /* get SEQ for mms id */
			ret = getMMSSEQDB(pDB,sqlca,buff,ack);
			//log_history(0,0,"[INF] PD14.get MMS SEQ 사용");
			break;
		case GETMMSID: /* get MMS ID */
			//PD15. get MMS ID 사용
			//log_history(0,0,"[INF] PD15.get MMS ID 사용X");
			ret = getMMSIDDB(pDB,sqlca,buff,ack);
			//log_history(0,0,"[INF] PD15.get MMS ID 사용");
			break;
		case GETCTNID: /* get CTN ID */
			//PD16. get CTN ID 사용X
			//log_history(0,0,"[INF] PD16.get CTN ID 사용X");
			break;
		case SETSENDCTNTBL: /* set MMS CTN TBL 사용X */
			//PD17. set MMS CTN TBL 사용X
			//log_history(0,0,"[INF] PD17.set MMS CTN TBL 사용X");
			break;
		case SETSENDTBL: /* set MMS TBL */
			//PD18. set MMS TBL 수정대상
			ret = setMMSTBL(pDB,sqlca,buff,ack);
			//log_history(0,0,"[INF] PD18.db set mms tbl insert-dstaddr[]cid[%s]nMMSId[%lld]",
			//pSenderDbMMSTBL->szDstAddr,
			//pSenderDbMMSTBL->szCid,
			//pSenderDbMMSTBL->nMMSId);
			if( ret < 0 )
			{
				log_history(0,0,"[ERR] PD18.db set mms tbl insert failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}
			break;
		/*case SETSENDQUE_V2: 
			//PD19. set MMS MSG 함수변경
			//ret = setMMSMSG(pDB,sqlca,buff,ack);
			ret = setMMSMSG_ATK_V2(pDB, sqlca, buff, ack);
			//log_history(0,0,"[INF]D19.db set mms msg queue insert v2-dstaddr[]nMMSId[%lld]",
			//pSenderDbMMSMSG->szDstAddr,
			//pSenderDbMMSMSG->nMMSId);
			if( ret < 0 )
			{
				log_history(0,0,"[ERR]PD19.db set mms msg queue insert failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}
			break;*/
		case SETSENDQUE_V3: /* set MMS MSG */
			//PD19. set MMS MSG 함수변경
			//ret = setMMSMSG(pDB,sqlca,buff,ack);
			ret = setMMSMSG_ATK_V3(pDB, sqlca, buff, ack);
			//log_history(0,0,"[INF]D19.db set mms msg queue insert v2-dstaddr[]nMMSId[%lld]",
			//pSenderDbMMSMSG->szDstAddr,
			//pSenderDbMMSMSG->nMMSId);
			if( ret < 0 )
			{
				log_history(0,0,"[ERR]PD19.db set mms msg queue insert failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}
			break;	
		case SETSENDQUE_V4: /* set MMS MSG */
			//PD19. set MMS MSG 함수변경
			//ret = setMMSMSG(pDB,sqlca,buff,ack);
			ret = setMMSMSG_ATK_V4(pDB, sqlca, buff, ack);
			//log_history(0,0,"[INF]D19.db set mms msg queue insert v2-dstaddr[]nMMSId[%lld]",
			//pSenderDbMMSMSG->szDstAddr,
			//pSenderDbMMSMSG->nMMSId);
			if( ret < 0 )
			{
				log_history(0,0,"[ERR]PD19.db set mms msg queue insert failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}
			break;		
		case SETSENDRPT: /* set SND ERR RPT */
			//PD20. set SND ERR REPORT 
			ret = setMMSRPTTBL(pDB, sqlca, buff, ack);
			if( ret < 0 )
			{
				log_history(0,0,"[ERR]PD20.db set SND ERR RPT insert failed");
				ret = errorDBprocess(pDB);
				alarm(0);
				goto ENDDOTHREAD;
			}
			break;	
		case SETRPTTBL: /* set MMS MSG */
			//PD20. set MMS MSG 사용X
			//log_history(0,0,"[INF]PD20.set MMS MSG 사용X");
			break;
		default: /* etc - error */
			break;
	}
	alarm(0);

    //PD18. ACK의 결과코드로 세팅
	sprintf(ack.szResult, "%02d", ret);

    //PD19. 소켓으로 값 전달
	ret = newSockfd.send((char*)&ack,sizeof(ack));
	if( ret == sizeof(ack))
	{
		/* commit */
		EXEC SQL CONTEXT USE :pDB;
		EXEC SQL COMMIT;

	} 
	else 
	{
		/* rollback */
		EXEC SQL CONTEXT USE :pDB;
		EXEC SQL ROLLBACK;
		log_history(0,0,"[ERR]socket_domain send ack failed ",strerror(errno));
	}


	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{
			log_history(0,0,"[ERR]semaphore wait failed");
			goto ENDDOTHREAD;
		}
  }
    /* 크리티컬 섹션 */
	dbConnQ.push(pDB);

	if( sem_post(&m_sem) == -1 )
	{
		log_history(0,0,"[ERR] semaphore clear failed - [%s]",strerror(errno));
		goto ENDDOTHREAD;
	}

ENDDOTHREAD:
	newSockfd.close();
	/* delete info; */
	return NULL;
}

/*
int setMMSMSG_ATK_V2(sql_context pDB, struct sqlca sqlca, char* buff, CSenderDbInfoAck& ack)
{
	CSenderDbMMSMSG_TALK* data = (CSenderDbMMSMSG_TALK*)buff;
	
	EXEC SQL BEGIN DECLARE SECTION;
		char szSqlErrorMsg[1024];
		int nSqlCode = -999;
		char szQName[32+1];
		int nQNum;
		int nPriority;
		char szSenderKey[40+1];
	    char szDstAddr[12+1];
	    char szTmplCd[30+1];
	    char szBtName[50+1];
		char szBtUrl[1000+1];
	    char szMsgBody[2000+1];
		char szResMethod[8+1];
		char szTimeout[2+1];
		
		long long nMMSId = 0;
		char cMMSId[20];
	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szSenderKey, 0x00, sizeof(szSenderKey));
    memset(szDstAddr, 0x00, sizeof(szDstAddr));
    memset(szTmplCd, 0x00, sizeof(szTmplCd));
    memset(szBtName, 0x00, sizeof(szBtName));
	memset(szBtUrl, 0x00, sizeof(szBtUrl));
    memset(szMsgBody, 0x00, sizeof(szMsgBody));
    memset(cMMSId, 0x00, sizeof(cMMSId));

	nQNum=1;
	//strcpy(szQName,data->szQName);
	nQNum = atoi(data->szQName);
	nPriority = data->nPriority;
	
	snprintf(szSenderKey, sizeof(szSenderKey), data->szSenderKey);
    snprintf(szDstAddr, sizeof(szDstAddr), data->szDstAddr);
    snprintf(szTmplCd, sizeof(szTmplCd), data->szTmplCd);
    snprintf(szBtName, sizeof(szBtName), data->szBtName);
	snprintf(szBtUrl, sizeof(szBtUrl), data->szBtUrl);
    snprintf(szMsgBody, sizeof(szMsgBody), data->szMsgBody);
	snprintf(szResMethod, sizeof(szResMethod), data->szResMethod);
	snprintf(szTimeout, sizeof(szTimeout), data->szTimeout);
    
	nMMSId  = data->nMMSId;

    sprintf(cMMSId,"%lld",data->nMMSId);
	
#ifdef TIME
    struct timeval timefirst, timesecond;
    struct timezone tzp;
    int secBuf, microsecBuf;
    float timeBuf;
	gettimeofday(&timefirst,&tzp);
#endif

	//log_history(0,0,"[INF] -1- nQNum[%d] nPriority[%d] szSenderKey[%s] szDstAddr[%s] szTmplCd[%s] szBtName[%s] szBtUrl[%s] szMsgBody[%s] cMMSId[%s]", 
	//nQNum, nPriority, szSenderKey, szDstAddr, szTmplCd, szBtName, szBtUrl, szMsgBody, cMMSId); 
	
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
		    PROC_SET_ATK_MSG_V2(in_q_num       =>:nQNum
			                   ,in_priority    =>:nPriority
							   ,in_sender_key  =>:szSenderKey
							   ,in_dst_addr    =>:szDstAddr
							   ,in_template_cd =>:szTmplCd
							   ,in_button_name =>:szBtName
							   ,in_button_url  =>:szBtUrl
							   ,in_msg_body    =>:szMsgBody
							   ,in_mms_id      =>:cMMSId
							   ,in_res_method  =>:szResMethod
							   ,in_timeout     =>:szTimeout
							   ,ot_sqlcode     =>:nSqlCode
							   ,ot_sqlmsg      =>:szSqlErrorMsg
							   );

		END;
	END-EXEC;
	
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call setMMSMSG_ATK_V2 exec failed - MMSID[%lld] templCd[%s] sqlcode[%d] sqlmsg[%s]",nMMSId,szTmplCd,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call setMMSMSG_ATK_V2 invaled failed - MMSID[%lld]templCd[%s]otReuslt[%d]errMsg[%s]",nMMSId,szTmplCd,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		//return 58;
		return -1;
	}
	
	ack.mmsid = nMMSId;
	
#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"setMMSMSG_ATK_V2 PROC_SET_TALK_MSG_V2 time [%f]",timeBuf);
#endif

    //return 55;
    return 1;
}*/

int setMMSMSG_ATK_V3(sql_context pDB, struct sqlca sqlca, char* buff, CSenderDbInfoAck& ack)
{
	CSenderDbMMSMSG_TALK* data = (CSenderDbMMSMSG_TALK*)buff;
	
	EXEC SQL BEGIN DECLARE SECTION;
		char szSqlErrorMsg[1024];
		int nSqlCode = -999;
		char szQName[32+1];
		int nQNum;
		int nPriority;
		char szSenderKey[40+1];
	    char szDstAddr[12+1];
	    char szTmplCd[30+1];
	    char szBtName[50+1];
		char szBtUrl[1000+1];
		char szButton[4000+1];
	    char szMsgBody[2000+1];
		char szResMethod[8+1];
		char szTimeout[5+1];
		char szTitle[50+1];
		
		long long nMMSId = 0;
		char cMMSId[20];
	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szSenderKey, 0x00, sizeof(szSenderKey));
    memset(szDstAddr, 0x00, sizeof(szDstAddr));
    memset(szTmplCd, 0x00, sizeof(szTmplCd));
    memset(szBtName, 0x00, sizeof(szBtName));
	memset(szBtUrl, 0x00, sizeof(szBtUrl));
	memset(szButton, 0x00, sizeof(szButton));
    memset(szMsgBody, 0x00, sizeof(szMsgBody));
	memset(szResMethod, 		0x00, sizeof(szResMethod));
	memset(szTimeout, 		0x00, sizeof(szTimeout));
    memset(cMMSId, 0x00, sizeof(cMMSId));
    memset(szTitle, 		0x00, sizeof(szTitle));

	nQNum=1;
	//strcpy(szQName,data->szQName);
	nQNum = atoi(data->szQName);
	nPriority = data->nPriority;
	
	snprintf(szSenderKey, sizeof(szSenderKey), data->szSenderKey);
    snprintf(szDstAddr, sizeof(szDstAddr), data->szDstAddr);
    snprintf(szTmplCd, sizeof(szTmplCd), data->szTmplCd);
    snprintf(szBtName, sizeof(szBtName), data->szBtName);
	//snprintf(szBtUrl, sizeof(szBtUrl), data->szBtUrl);
	strncpy(szBtUrl, data->szBtUrl, sizeof(szBtUrl)-1);
	//snprintf(szButton, sizeof(szButton), data->szButton);
	strncpy(szButton, data->szButton, sizeof(szButton)-1);
    //snprintf(szMsgBody, sizeof(szMsgBody), data->szMsgBody);
    strncpy(szMsgBody, data->szMsgBody, sizeof(szMsgBody)-1);
	snprintf(szResMethod, sizeof(szResMethod), data->szResMethod);
	snprintf(szTimeout, sizeof(szTimeout), data->szTimeout);
    strncpy(szTitle, data->szTitle, sizeof(szTitle)-1);
    
	nMMSId  = data->nMMSId;

    sprintf(cMMSId,"%lld",data->nMMSId);
    
	
#ifdef TIME
    /* gettimeofday */
    struct timeval timefirst, timesecond;
    struct timezone tzp;
    int secBuf, microsecBuf;
    float timeBuf;
	gettimeofday(&timefirst,&tzp);
    /* gettimeofday */
#endif

	//log_history(0,0,"[INF] -1- nQNum[%d] nPriority[%d] szSenderKey[%s] szDstAddr[%s] szTmplCd[%s] szBtName[%s] szBtUrl[%s] szMsgBody[%s] cMMSId[%s]", 
	//nQNum, nPriority, szSenderKey, szDstAddr, szTmplCd, szBtName, szBtUrl, szMsgBody, cMMSId); 
	
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
		    PROC_SET_ATK_MSG_V3(in_q_num       =>:nQNum
			                   ,in_priority    =>:nPriority
							   ,in_sender_key  =>:szSenderKey
							   ,in_dst_addr    =>:szDstAddr
							   ,in_template_cd =>:szTmplCd
							   ,in_button_name =>:szBtName
							   ,in_button_url  =>:szBtUrl
							   ,in_button      =>:szButton
							   ,in_msg_body    =>:szMsgBody
							   ,in_mms_id      =>:cMMSId
							   ,in_res_method  =>:szResMethod
							   ,in_timeout     =>:szTimeout
							   ,in_title       =>:szTitle
							   ,ot_sqlcode     =>:nSqlCode
							   ,ot_sqlmsg      =>:szSqlErrorMsg
							   );

		END;
	END-EXEC;
	
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call setMMSMSG_ATK_V3 exec failed - MMSID[%lld] templCd[%s] szMsgBodylen[%d] szDstAddr[%s] sqlcode[%d] sqlmsg[%s]",nMMSId,szTmplCd,szDstAddr,strlen(szMsgBody),sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call setMMSMSG_ATK_V3 invaled failed - MMSID[%lld]templCd[%s]szDstAddr[%s]szMsgBodylen[%d]otReuslt[%d]errMsg[%s]",nMMSId,szTmplCd,szDstAddr,strlen(szMsgBody),nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		//return 58;
		return -1;
	}
	
	ack.mmsid = nMMSId;
	
#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"setMMSMSG_ATK_V2 setMMSMSG_ATK_V3 time [%f]",timeBuf);
#endif

    //return 55;
    return 1;
}


int setMMSMSG_ATK_V4(sql_context pDB, struct sqlca sqlca, char* buff, CSenderDbInfoAck& ack)
{
	CSenderDbMMSMSG_TALK* data = (CSenderDbMMSMSG_TALK*)buff;
	
	int tmpI = 0;
		   
	EXEC SQL BEGIN DECLARE SECTION;
		char szSqlErrorMsg[1024];
		int  nSqlCode = -999;
		char szQName[32+1];
		int  nQNum;
		int  nPriority;
		char szSenderKey[40+1];
		char szDstAddr[12+1];
		char szTmplCd[30+1];
		char szBtName[50+1];
		char szBtUrl[1000+1];
		char szButton[4000+1];
		char szMsgBody[2000+1];
		char szResMethod[8+1];
		//char szTimeout[2+1];
		char szTimeout[5+1];
		char szTitle[50+1];
		char szCurType[3+1];//currency type
		char szMessageType[2+1];//알림톡메세지타입
		char szKkoHeader[16+1];//kko제목
		char szAttachment[4000+1];//버튼 아이템
		char szSupplement[4000+1];//바로연결
		
		long long nMMSId = 0;
		char cMMSId[20];
		char szPrice[10+1]; //price
	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg, 0x00, sizeof(szSqlErrorMsg));
	memset(szQName, 0x00, sizeof(szQName));
	memset(szSenderKey, 	0x00, sizeof(szSenderKey));
	memset(szDstAddr, 		0x00, sizeof(szDstAddr));
	memset(szTmplCd, 			0x00, sizeof(szTmplCd));
	memset(szBtName, 			0x00, sizeof(szBtName));
	memset(szBtUrl, 			0x00, sizeof(szBtUrl));
	memset(szButton, 			0x00, sizeof(szButton));
	memset(szMsgBody, 		0x00, sizeof(szMsgBody));
	memset(szResMethod, 		0x00, sizeof(szResMethod));
	memset(szTimeout, 		0x00, sizeof(szTimeout));
	memset(cMMSId, 				0x00, sizeof(cMMSId));
	memset(szTitle, 		0x00, sizeof(szTitle));
	memset(szPrice, 0x00, sizeof(szPrice));
	memset(szCurType, 0x00, sizeof(szCurType));
	memset(szMessageType, 0x00, sizeof(szMessageType));
	memset(szKkoHeader, 0x00, sizeof(szKkoHeader));
	memset(szAttachment, 0x00, sizeof(szAttachment));
	memset(szSupplement, 0x00, sizeof(szSupplement));
	

	nQNum=1;
	//strcpy(szQName,data->szQName);
	nQNum = atoi(data->szQName);
	nPriority = data->nPriority;
	
	snprintf(szSenderKey, sizeof(szSenderKey), data->szSenderKey);
	snprintf(szDstAddr, 	sizeof(szDstAddr), data->szDstAddr);
	snprintf(szTmplCd, 		sizeof(szTmplCd), data->szTmplCd);
	snprintf(szBtName, 		sizeof(szBtName), data->szBtName);
	
	//snprintf(szBtUrl, sizeof(szBtUrl), data->szBtUrl);
	strncpy(szBtUrl, data->szBtUrl, sizeof(szBtUrl)-1);
	//snprintf(szButton, sizeof(szButton), data->szButton);
	strncpy(szButton, data->szButton, sizeof(szButton)-1);
	strncpy(szMessageType, data->szMessageType, sizeof(szMessageType)-1);
	strncpy(szKkoHeader, data->szKkoHeader, sizeof(szKkoHeader)-1);
	strncpy(szAttachment, data->szAttachment, sizeof(szAttachment)-1);
	strncpy(szSupplement, data->szSupplement, sizeof(szSupplement)-1);
	//snprintf(szMsgBody, sizeof(szMsgBody), data->szMsgBody);
	strncpy(szMsgBody, data->szMsgBody, sizeof(szMsgBody)-1);
	
	snprintf(szResMethod, sizeof(szResMethod), data->szResMethod);
	snprintf(szTimeout, 	sizeof(szTimeout), data->szTimeout);
	
	strncpy(szTitle, data->szTitle, sizeof(szTitle)-1);
    
	nMMSId  = data->nMMSId;
	
	sprintf(cMMSId,"%lld",data->nMMSId);
	sprintf(szPrice,"%s",data->szPrice);
	
	strncpy(szCurType, data->szCurType, sizeof(szCurType)-1);
    
	
#ifdef TIME
    /* gettimeofday */
    struct timeval timefirst, timesecond;
    struct timezone tzp;
    int secBuf, microsecBuf;
    float timeBuf;
	gettimeofday(&timefirst,&tzp);
    /* gettimeofday */
#endif

	//log_history(0,0,"[INF] -1- nQNum[%d] nPriority[%d] szSenderKey[%s] szDstAddr[%s] szTmplCd[%s] szBtName[%s] szBtUrl[%s] szMsgBody[%s] cMMSId[%s]", 
	//nQNum, nPriority, szSenderKey, szDstAddr, szTmplCd, szBtName, szBtUrl, szMsgBody, cMMSId); 
	
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
		    PROC_SET_ATK_MSG_V4(in_q_num       =>:nQNum
			                   ,in_priority    =>:nPriority
							   				 ,in_sender_key  =>:szSenderKey
							   				 ,in_dst_addr    =>:szDstAddr
							   				 ,in_template_cd =>:szTmplCd
							   				 ,in_button_name =>:szBtName
							   				 ,in_button_url  =>:szBtUrl
							   				 ,in_button      =>:szButton
							   				 ,in_msg_body    =>:szMsgBody
							   				 ,in_mms_id      =>:cMMSId
							   				 ,in_res_method  =>:szResMethod
							   				 ,in_timeout     =>:szTimeout
							   				 ,in_title       =>:szTitle
							   				 ,in_price       =>:szPrice
												 ,in_cur_type    =>:szCurType	
												 ,in_message_type    =>:szMessageType	
												 ,in_kko_header    =>:szKkoHeader	
												 ,in_attachment    =>:szAttachment	
												 ,in_supplement    =>:szSupplement		
							   				 ,ot_sqlcode     =>:nSqlCode
							   				 ,ot_sqlmsg      =>:szSqlErrorMsg
							   				 );

		END;
	END-EXEC;
	
	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call setMMSMSG_ATK_V4 exec failed - MMSID[%lld] templCd[%s] szMsgBodylen[%d] szDstAddr[%s] sqlcode[%d] sqlmsg[%s]",nMMSId,szTmplCd,szDstAddr,strlen(szMsgBody),sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,strlen(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call setMMSMSG_ATK_V4 invaled failed - MMSID[%lld]templCd[%s]szDstAddr[%s]szMsgBodylen[%d]otReuslt[%d]errMsg[%s]",nMMSId,szTmplCd,szDstAddr,strlen(szMsgBody),nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		//return 58;
		return -1;
	}
	
	ack.mmsid = nMMSId;
	
#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"setMMSMSG_ATK_V4 time [%f]",timeBuf);
#endif

    //return 55;
    return 1;
}

int setMMSTBL(sql_context pDB,struct sqlca sqlca, char* buff,CSenderDbInfoAck& ack)
{
	CSenderDbMMSTBL* data = (CSenderDbMMSTBL*)buff;

	EXEC SQL BEGIN DECLARE SECTION;

		char szSqlErrorMsg[1024];
		int nSqlCode = -999;

		char szDstAddr[16+1];
		char szCallBack[16+1];
		char szMsgTitle[64+1];
		char szPtnSn[32+1];
		char szResvData[200+1];
		char szCid[10+1];
		int nMsgType = 0;
		int nPriority = 0;
		int nCtnId = 0;
		int nCtnType = 0;
		int nRgnRate = 0;
		int nInterval = 0;
		int nTextCnt = 0;
		int nImgCnt = 0;
		int nAugCnt = 0;
		int nMpCnt = 0;
		long long nMMSId = 0;
		char cMMSId[20];
		char szSenderKey[40+1];

	EXEC SQL END DECLARE SECTION;

	memset(szSqlErrorMsg ,0x00, sizeof(szSqlErrorMsg ));   //CCL(szSqlErrorMsg);
	memset(szCid         ,0x00, sizeof(szCid         ));   //CCL(szCid);        
	memset(szDstAddr     ,0x00, sizeof(szDstAddr     ));   //CCL(szDstAddr);    
	memset(szCallBack    ,0x00, sizeof(szCallBack    ));   //CCL(szCallBack);   
	memset(szMsgTitle    ,0x00, sizeof(szMsgTitle    ));   //CCL(szMsgTitle);   
	memset(szPtnSn       ,0x00, sizeof(szPtnSn       ));   //CCL(szPtnSn);      
	memset(szResvData    ,0x00, sizeof(szResvData    ));   //CCL(szResvData);   
	memset(cMMSId		 ,0x00, sizeof(cMMSId		 ));
	memset(szSenderKey	 ,0x00, sizeof(szSenderKey	 ));
	
	strcpy(szDstAddr, data->szDstAddr);
	strcpy(szCallBack, data->szCallBack);
	sprintf(szMsgTitle, "%.*s", sizeof(szMsgTitle)-1, data->szMsgTitle);
	strcpy(szPtnSn, data->szPtnSn);
	strcpy(szResvData, data->szResvData);
	memcpy(szCid, data->szCid, 10);
	
	sprintf(cMMSId,"%lld", data->nMMSId);

	snprintf(szSenderKey, sizeof(szSenderKey), data->szSenderKey);
	
	nMsgType 	= data->nMsgType;
	nPriority 	= data->nPriority;
	nCtnId 		= data->nCtnId;
	nCtnType 	= data->nCtnType;
	nRgnRate 	= data->nRgnRate;
	nInterval 	= data->nInterval;
	nTextCnt 	= data->nTextCnt;
	nImgCnt 	= data->nImgCnt;
	nAugCnt 	= data->nAugCnt;
	nMpCnt 		= data->nMpCnt;
	nMMSId 		= data->nMMSId;
	
	
//	log_history(0,0,"[DBG] MMSID[%d]",data->nMMSId);

#ifdef TIME
	/* gettimeofday */
	struct timeval timefirst, timesecond;
	struct timezone tzp;
	int secBuf, microsecBuf;
	float timeBuf;
	gettimeofday(&timefirst,&tzp);
	/* gettimeofday */
#endif

    //TBL_MMS_SEND 테이블에 insert
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_tbl(in_dstaddr =>:szDstAddr
											,in_callback =>:szCallBack
											,in_msgtitle =>:szMsgTitle
											,in_ptn_sn =>:szPtnSn
											,in_resv_data =>:szResvData
											,in_cid =>:szCid
											,in_msg_type =>:nMsgType
											,in_priority =>:nPriority
											,in_ctn_id =>:nCtnId
											,in_ctn_type =>:nCtnType
											,in_rgn_rate =>:nRgnRate
											,in_interval => :nInterval
											,in_text_cnt => :nTextCnt
											,in_img_cnt => :nImgCnt
											,in_aud_cnt => :nAugCnt
											,in_mp_cnt => :nMpCnt
											,in_mms_id => :cMMSId
											,in_sender_key => :szSenderKey
											,ot_sqlcode =>:nSqlCode
											,ot_sqlmsg =>:szSqlErrorMsg
               				);
		END;
	END-EXEC;

	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call proc_set_mms_tbl exec failed - MMSID[%lld] CID[%s] ptnSn[%s] sqlcode[%d] sqlmsg[%s]",nMMSId,szCid,szPtnSn,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call proc_set_mms_tbl invaled failed - MMSID[%lld]CID[%s]ptnSn[%s]otReuslt[%d]errMsg[%s]",nMMSId,szCid,szPtnSn,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		//return 58;
		return -1;
	}
	
	ack.mmsid = nMMSId;
    
#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"setMMSTBL proc_set_mms_tbl time [%f]",timeBuf);
#endif
	//return 55;
	return 1;
}

//사용
int getMMSSEQDB(sql_context pDB,struct sqlca sqlca, char* buff,CSenderDbInfoAck& ack)
{
	CSenderDbMMSSEQ* data = (CSenderDbMMSSEQ*)buff;
	
	EXEC SQL BEGIN DECLARE SECTION;

		char szSqlErrorMsg[1024];
		int seq = 0;
		int nSqlCode = -999;

	EXEC SQL END DECLARE SECTION;
	
	memset(szSqlErrorMsg,0x00,sizeof(szSqlErrorMsg));		//CCL(szSqlEr
	
	
    
#ifdef TIME
	/* gettimeofday */
	struct timeval timefirst, timesecond;
	struct timezone tzp;
	int secBuf, microsecBuf;
	float timeBuf;
	gettimeofday(&timefirst,&tzp);
	/* gettimeofday */
#endif

	//MSG ID 가져오기
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	
	EXEC SQL DECLARE cur_proc_id CURSOR FOR
		SELECT SEQ_PROC_ID.NEXTVAL FROM DUAL;

	
	EXEC SQL OPEN cur_proc_id;

	if(sqlca.sqlcode!= 0)
	{
		log_history(0,0,"[ERR] cur_proc_id OPEN failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
		return 58;		
	}
	

	while(true)
	{

		EXEC SQL FETCH cur_proc_id INTO
			:seq ; 	

		//log_history(0,0,"[INF] cur_proc_id seq[%d] sqlcode[%d] sqlmsg[%s]",seq, sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
		    			
		if( seq > 0 )
			break;
				
		if(sqlca.sqlcode != 0)
		{
            log_history(0,0,"[ERR] cur_proc_id FETCH failed - seq[%d] sqlcode[%d] sqlmsg[%s]", seq, sqlca.sqlcode, trim(sqlca.sqlerrm.sqlerrmc, strlen(sqlca.sqlerrm.sqlerrmc)));
	        
			EXEC SQL CLOSE cur_proc_id;            
			return 58;
		}

	}
	
	ack.seq = seq;
	
	EXEC SQL CLOSE cur_proc_id;

#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"getMMSSEQDB cur_proc_id time [%f]",timeBuf);
#endif

	return 55;
}

//사용
int getMMSIDDB(sql_context pDB,struct sqlca sqlca, char* buff,CSenderDbInfoAck& ack)
{
	CSenderDbMMSID* data = (CSenderDbMMSID*)buff;
	
	EXEC SQL BEGIN DECLARE SECTION;

		char szSqlErrorMsg[1024];
		char szCid[10+1];
		long long nMMSID = 0;
		int nCTNID = 0;
		int nSqlCode = -999;

	EXEC SQL END DECLARE SECTION;
	
	memset(szSqlErrorMsg,0x00,sizeof(szSqlErrorMsg));		//CCL(szSqlEr
	memset(szCid        ,0x00,sizeof(szCid        ));		//CCL(szCid);

	memcpy(szCid,data->szCid,10);
    
#ifdef TIME
	/* gettimeofday */
	struct timeval timefirst, timesecond;
	struct timezone tzp;
	int secBuf, microsecBuf;
	float timeBuf;
	gettimeofday(&timefirst,&tzp);
	/* gettimeofday */
#endif

	//MSG ID 가져오기
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
			proc_get_mms_id(in_cid =>:szCid
								,ot_mms_id =>:nMMSID
								,ot_sqlcode =>:nSqlCode
								,ot_sqlmsg =>:szSqlErrorMsg
								);
		END;
	END-EXEC;

	//log_history(0,0,"[DBG] setMMSTBL MMSID[%lld]",nMMSID);

	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call proc_get_mms_id exec failed - MMSID[%lld] CID[%s] sqlcode[%d] sqlmsg[%s]",nMMSID,szCid,sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call proc_get_mms_id invaled failed - MMSID[%lld]CID[%s]otReuslt[%d]errMsg[%s]",nMMSID,szCid,nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		return 58;
	}
	ack.mmsid = nMMSID;

#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"getMMSIDDB proc_get_mms_id time [%f]",timeBuf);
#endif

	return 55;
}




void Init_Oracle(sql_context ctx)
{
	EXEC SQL BEGIN DECLARE SECTION;

		VARCHAR Username[10];
		VARCHAR Password[10];
		VARCHAR dbstring[10];

	EXEC SQL END DECLARE SECTION;

	strcpy((char*)Username.arr, gConf.dbID);
	Username.len = strlen((char*)Username.arr);
	strcpy((char*)Password.arr, gConf.dbPASS);
	Password.len = strlen((char*)Password.arr);
	strcpy((char*)dbstring.arr, gConf.dbSID);
	dbstring.len = strlen((char*)dbstring.arr);

	EXEC SQL CONTEXT USE :ctx;
	EXEC SQL CONNECT :Username IDENTIFIED BY :Password USING :dbstring ;
}


int errorDBprocess(void* pDB)
{
	CKSThread dbErr;
	int ret;
	pthread_t tid;

	ret = dbErr.create(&(tid),NULL,doDBError,(void*)pDB);
	if( ret != 0 )
	{
		log_history(0,0,"[ERR] thread dberr process create failed - errno[%s] DBqueSize[%d]",strerror(errno),dbConnQ.size());
	}
	return -1;
}


void* doDBError(void* param)
{
	pthread_detach(pthread_self());
	struct sqlca sqlca;

	EXEC SQL BEGIN DECLARE SECTION;

		sql_context pDB = (sql_context)param;

	EXEC SQL END DECLARE SECTION;

DBERRCONN:

	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL COMMIT WORK RELEASE;
	EXEC SQL CONTEXT FREE :pDB;

	if( activeProcess == false )
	{
		return NULL;
	}

	EXEC SQL CONTEXT ALLOCATE :pDB;

	Init_Oracle(pDB);
    
	if( sqlca.sqlcode !=0 )
	{
		monitoring("[INF] db connection - failed",0,errno);
		wait_a_moment(900000);
		goto DBERRCONN;
	}

	log_history(0,0,"[INF] db sql_context - [%x]",pDB);
	while( sem_wait(&m_sem) == -1 )
	{
		if(errno != EINTR )
		{
			log_history(0,0,"[ERR] semaphore wait failed");
			wait_a_moment(900000);
			goto DBERRCONN;
		}
	}
	/* 크리티컬 섹션 */
	dbConnQ.push(pDB);

	if( sem_post(&m_sem) == -1 )
	{
		log_history(0,0,"[ERR] semaphore clear failed - errno[%s]pDB[%x]",strerror(errno),pDB);
		return NULL;
	}

	log_history(0,0,"[INF] db connection - current queSize[%d]",dbConnQ.size());

	return NULL;
}

int getTelcoId(char* szTelco,char* szDstaddr)
{
	char* p;
	int telcoArray[7];
	char CID[4];
	int i=0;
	memset(telcoArray,0x00,sizeof(telcoArray));
	memset(CID,0x00,sizeof(CID));	//CCL(CID);

	p = strtok(szTelco,"|");
	if( p == NULL )
	{
		return gConf.telcoDefaultID;
	}
	telcoArray[0] = atoi(p);

	while(p = strtok(NULL,"|") )
	{
		telcoArray[++i]= atoi(p);
		if( i > 6 )
		{
			break;
		}
	}

	memcpy(CID,szDstaddr,3);

	switch(atoi(CID))
	{
		case 11 :
			return telcoArray[0];
		case 16 :
			return telcoArray[1];
		case 17 :
			return telcoArray[2];
		case 18 :
			return telcoArray[3];
		case 19 :
			return telcoArray[4];
		case 10 :
			return telcoArray[5];
		case 50 :
			return telcoArray[6];
	}

	return gConf.telcoDefaultID;
}

int offerInfo(CKSSocket& newSockfd)
{
	char szQueSize[4];
	memset(szQueSize,0x00,sizeof(szQueSize));//  CCL(szQueSize);
	sprintf(szQueSize,"%d",dbConnQ.size());

//	return mod
//	newSockfd.send(szQueSize,strlen(szQueSize));
//	return 0;
	return newSockfd.send(szQueSize,strlen(szQueSize));
}

int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	CKSConfig conf;

	if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}
	conf.strncpy2(gConf.senderDBName , conf.FetchEntry("domain.senderdb"),64);
	if( gConf.senderDBName == NULL )
	{
		strcpy(gConf.senderDBName,"");
	}

	gConf.telcoDefaultID = conf.FetchEntryInt("telco.defaultid");
	gConf.dbFindTimeOut = conf.FetchEntryInt("db.findtimeout");
	
	if( gConf.dbFindTimeOut <= 0 )
	{
		gConf.dbFindTimeOut = 2;
	}

	conf.strncpy2(gConf.dbID , conf.FetchEntry("db.id"),16);
	if( gConf.dbID == NULL )
	{
		strcpy(gConf.dbID,"");
	}

	conf.strncpy2(gConf.dbPASS , conf.FetchEntry("db.pass"),16);
	if( gConf.dbPASS == NULL )
	{
		strcpy(gConf.dbPASS,"");
	}

	conf.strncpy2(gConf.dbSID , conf.FetchEntry("db.sid"),16);
	if( gConf.dbSID == NULL )
	{
		strcpy(gConf.dbSID,"");
	}

	return 0;
}
/*사용x
int setRPTTBL(sql_context pDB, struct sqlca sqlca, char* buff, CSenderDbInfoAck& ack)
{
	CSenderDbMMSRPTTBL* data = (CSenderDbMMSRPTTBL*)buff;

	EXEC SQL BEGIN DECLARE SECTION;
	
	char szSqlErrorMsg[1024];
	int nSqlCode = -999;

	long long mms_id;
	char msg_id[4096] = {0x00,};
	char snd_numb[4096] = {0x00,};
	char rcv_numb[4096] = {0x00,};
	int res_code;
	char res_text[4096] = {0x00,};
	int telco_id;
	int res_type;
	char end_telco[4096] = {0x00,};

	EXEC SQL END DECLARE SECTION;

	//초기화
	mms_id = 0;
	memset(msg_id, 0x00, sizeof(msg_id));   
	memset(snd_numb, 0x00, sizeof(snd_numb));
	memset(rcv_numb, 0x00, sizeof(rcv_numb));
	res_code = 0;
	memset(res_text, 0x00, sizeof(res_text));
	telco_id = 0;
	res_type = 0;
	memset(end_telco, 0x00, sizeof(end_telco));

	//값 세팅	
	mms_id = data->nMMSId;
	sprintf(msg_id, "%ld", data->nMMSId);
	sprintf(snd_numb, data->szCallBack);
	sprintf(rcv_numb, data->szDstAddr);
	res_code = data->res_code;
	sprintf(res_text, data->res_text);
	telco_id = 0;

    //TBL_MMS_RPT에 입력
	EXEC SQL WHENEVER SQLERROR CONTINUE;
	EXEC SQL CONTEXT USE :pDB;
	EXEC SQL EXECUTE
		BEGIN
       	PROC_SET_RPT_FORCE(:mms_id, :msg_id, :snd_numb, :rcv_numb, :res_code, 
							:res_text, :telco_id, :res_type, :end_telco, :nSqlCode, :szSqlErrorMsg);
		END;
	END-EXEC;

	if( nSqlCode != 0 )
	{
		if( nSqlCode == -999 )
		{
			log_history(0,0,"[ERR] db call PROC_SET_RPT_FORCE exec failed - sqlcode[%d] sqlmsg[%s]",sqlca.sqlcode,trim(sqlca.sqlerrm.sqlerrmc ,sizeof(sqlca.sqlerrm.sqlerrmc)));
			return -1;
		}
		log_history(0,0,"[ERR] db call PROC_SET_RPT_FORCE invaled failed - otReuslt[%d]errMsg[%s]",nSqlCode,trim(szSqlErrorMsg,strlen(szSqlErrorMsg)));
		return 58;
	}
	
	ack.mmsid = data->nMMSId;
    
	return 55;
}*/

int setMMSRPTTBL(sql_context pDB,struct sqlca sqlca, char* buff,CSenderDbInfoAck& ack)
{
	CSenderDbMMSRPTQUE* data = (CSenderDbMMSRPTQUE*)buff;
	
	EXEC SQL BEGIN DECLARE SECTION;
	int ot_sqlcode = -1;
	char ot_sqlmsg[1024];
	int ot_queue_sqlcode = -1;
	char ot_queue_sqlmsg[1024];

	char msg_id[50+1];
	long long mms_id;
	char cmms_id[32+1];
	char dlv_date[14+1];
	char snd_numb[12+1];
	char rcv_numb[12+1];
	char res_code[4+1];
	char res_text[200+1];
	char cid[10+1];
	char ptn_sn[32+1];
	int telco_id;
	int res_type;
	char end_telco[5+1];
	char resv_data[200+1];
	

	EXEC SQL END DECLARE SECTION;
	
	int type;
	string cTime;
	time_t tm_time;
	struct tm *st_time;
	char timeBuff[1024] = {0x00,};

	memset(ot_sqlmsg, 0x00, sizeof ot_sqlmsg);
	memset(ot_queue_sqlmsg, 0x00, sizeof ot_queue_sqlmsg);
	memset(msg_id, 0x00, sizeof msg_id);
	memset(dlv_date, 0x00, sizeof dlv_date);
	memset(snd_numb, 0x00, sizeof snd_numb);
	memset(rcv_numb, 0x00, sizeof rcv_numb);
	memset(res_code, 0x00, sizeof res_code);
	memset(res_text, 0x00, sizeof res_text);
	memset(cid, 0x00, sizeof cid);
	memset(ptn_sn, 0x00, sizeof ptn_sn);
	memset(end_telco, 0x00, sizeof end_telco);
	memset(cmms_id,	0x00, sizeof(cmms_id));
	memset(resv_data,	0x00, sizeof(resv_data));

	char sNum[24+1];
	char rNum[24+1];
	memset(sNum, 0x00, 24+1);
	memset(rNum, 0x00, 24+1);
	
	//map<string, string>::iterator FindItr;
	
	//FindItr = mapReport.find("msg_id");
	
	mms_id = data->nMMSId;
	
	sprintf(cmms_id,"%lld", mms_id);
	
	sprintf(msg_id, "%lld", mms_id); //msg_id
	
	sprintf(cid, "%s", data->szCid); //msg_id
	
	time(&tm_time);
	st_time = localtime(&tm_time);
	strftime(timeBuff, 1024, "%Y%m%d%H%M%S", st_time);
	cTime = timeBuff;
	
	sprintf(dlv_date, "%s", cTime.c_str()); //reg_snd_dttm
	if (strcmp(dlv_date, "") == 0 || strcmp(dlv_date, "              ") == 0)
	{//통신사로 발송된 시간이 없으면 임의의 값 입력
		strcpy(dlv_date,"19710101000000");
	}

			
	sprintf(res_code, "%d", data->res_code); //rslt_val

	sprintf(end_telco, "%s", "KKO");

	sprintf(res_text, "%s", data->res_text);
	
	sprintf(ptn_sn, "%s", data->szPtnSn);
	
	telco_id = data->nTelcoId;
	res_type = 0;

	type = data->nType;
	//strcpy(snd_numb, sNum);
	//FindItr = mapReport.find("rcv_numb");
	//sprintf(rcv_numb, FindItr->second.c_str());
	
	if(type != 1)
	{
		sprintf(rcv_numb, "%s", data->szDstAddr);
	}
	
	//int retry_cnt = 0;
	
	
#ifdef TIME
    /* gettimeofday */
    struct timeval timefirst, timesecond;
    struct timezone tzp;
    int secBuf, microsecBuf;
    float timeBuf;
	gettimeofday(&timefirst,&tzp);
    /* gettimeofday */
#endif

	//log_history(0,0,"[INF] -1- nQNum[%d] nPriority[%d] szSenderKey[%s] szDstAddr[%s] szTmplCd[%s] szBtName[%s] szBtUrl[%s] szMsgBody[%s] cMMSId[%s]", 
	//nQNum, nPriority, szSenderKey, szDstAddr, szTmplCd, szBtName, szBtUrl, szMsgBody, cMMSId); 
	
	
	if(type == 1)
	{
		EXEC SQL CONTEXT USE :pDB;
		EXEC SQL EXECUTE
		BEGIN
			proc_set_rpt_skb(:cmms_id, :msg_id, :dlv_date, :snd_numb, :rcv_numb, :res_code, :res_text, :telco_id, :res_type, :end_telco, :ot_sqlcode, :ot_sqlmsg, :ot_queue_sqlcode, :ot_queue_sqlmsg);
		END;
		END-EXEC;
    	
		if( ot_sqlcode != 0 )
		{
			log_history(0,0,"[ERR]db proc_set_rpt_skb SNDTBL ERROR MMSID[%lld][%d][%.100s]", mms_id, ot_sqlcode, ot_sqlmsg);
			return -1;
		}
		
		if( ot_queue_sqlcode != 0 )
		{
			log_history(0,0,"[ERR]db proc_set_rpt_skb SNDTBL queue ERROR MMSID[%lld][%d][%.100s]", mms_id, ot_queue_sqlcode, ot_queue_sqlmsg);
			return -1;
		}
	}
	else
	{
		EXEC SQL CONTEXT USE :pDB;
		EXEC SQL EXECUTE
		BEGIN
			proc_set_mms_rpt_queue(:cid, :ptn_sn, :res_code, :telco_id, :end_telco, :cmms_id, :resv_data, :rcv_numb, :dlv_date, :ot_sqlcode, :ot_sqlmsg);
		END;
		END-EXEC;
    	
		if( ot_sqlcode != 0 )
		{
			log_history(0,0,"[ERR]db proc_set_mms_rpt_queue SND queue ERROR MMSID[%lld][%d][%.100s]", mms_id, ot_sqlcode, ot_sqlmsg);
			return -1;
		}
	}
	
	ack.mmsid = mms_id;
	
#ifdef TIME
	gettimeofday(&timesecond,&tzp);
	secBuf = (timesecond.tv_sec - timefirst.tv_sec);
	microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
	timeBuf = microsecBuf;
	timeBuf = timeBuf / 1000000;
	timeBuf = timeBuf + secBuf;
	log_history(0,0,"setMMSRPTTBL time [%f]",timeBuf);
#endif

    //return 55;
    return 1;
}


