#include "monitor.h"
#include "kssocket.h"
#include "ksthread.h"


class CConfigMonitor {
    public:
        char domainName[64];
};

CConfigMonitor gConf;



int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];


int configParse(char* file);

class CThreadInfo {
    public:
        pthread_t tid;
        int sock; /* domain socket */
};


void* doService(void* param);


int main(int argc, char* argv[])
{
    pid_t childpid;
    int ret;
    char logMsg[SOCKET_BUFF];
    struct timeval outtime;
    CKSSocket svrSockfd;
    CKSSocket newSockfd;
    CKSThread ksthread;
    char buff[SOCKET_BUFF];
    int hNewSocket;
    CThreadInfo* pThreadInfo;
    int i;

    Init_Server();

    if (ml_sub_init(PROCESS_NO, PROCESS_NAME,(char*)0,(char*)0)<0) {
        perror("ml_sub_init Error.");
        ml_sub_end();
        exit(1);
    }


    ret = configParse(argv[1]);
    if( ret != 0 )
    {
        exit(1);
    }




    sprintf(logMsg,"%s START",PROCESS_NAME);
    monitoring(logMsg,0,0);


    ret = svrSockfd.createDomainNon(gConf.domainName);
    if( ret !=0 ) {
        log_history(0,0,"DOMAIN_MONITOR 생성실패[%s]",
                strerror(errno),
                gConf.domainName);
        goto END;
    }
   

    while(activeProcess)
    {
        wait_a_moment(100000);

        hNewSocket = svrSockfd.accept();
        if( hNewSocket <= 0 )
            continue;


        /* new connection  */
        pThreadInfo = NULL;
        pThreadInfo = new CThreadInfo;
        if( pThreadInfo == NULL )
        {
            log_history(0,0,"new ThreadInfo Error [%s]",
                    strerror(errno));
            close(hNewSocket);
            continue;
        }

/*    log_history(0,0,"org[%x]",pThreadInfo); */
        pThreadInfo->sock = hNewSocket;
        //log_history(0,0,"thread start");
        ret = ksthread.create(&(pThreadInfo->tid),NULL,doService,(void*)pThreadInfo);
        //log_history(0,0,"thread end[%d]",ret);
        if( ret != 0 )
        {
            log_history(0,0,"create thread Error [%s]",
                    strerror(errno));
            close(pThreadInfo->sock);
            delete pThreadInfo;
            continue;
        }
        pthread_join(pThreadInfo->tid,NULL);

            delete pThreadInfo;

    }

END:
    svrSockfd.close();
    
    sprintf(logMsg,"%s CLOSE",PROCESS_NAME);
    monitoring(logMsg,0,0);
    ml_sub_end();

    return 0;
}


void* doService(void* param)
{
   // pthread_detach(pthread_self());
    //log_history(0,0,"doService start1");
    int ret;
    CKSSocket newSockfd;
    char buff[SOCKET_BUFF];
    int sock;
    CThreadInfo* info = (CThreadInfo*)param;
    newSockfd.attach(info->sock);

    CCL(buff);
    //log_history(0,0,"doService start2");

    ret = newSockfd.rcvmsg(buff);
    if( ret == 0 ) {
        newSockfd.close();
        log_history(0,0,"connection 후 3초간 데이터가 없어 연결을 close 합니다");
//        delete info;
        return NULL;
    }

    if( ret < 0 ) {
        newSockfd.close();
        log_history(0,0,"recv Error");
 //       delete info;
        return NULL;
    }

/*    viewPack(buff,sizeof(CMonitor));
 */
     struct sockaddr_in serv_addr;

    //log_history(0,0,"socket write start");
    sock=socket(PF_INET,SOCK_DGRAM,0);
    memset(&serv_addr,0,sizeof(serv_addr));
    serv_addr.sin_family=AF_INET;
    //serv_addr.sin_addr.s_addr=inet_addr("211.43.202.31");
    serv_addr.sin_addr.s_addr=inet_addr("10.216.243.231");
    serv_addr.sin_port=htons(50000);
    connect(sock,(struct sockaddr*)&serv_addr,sizeof(serv_addr));
    ret = write(sock,buff,sizeof(CMonitor));
    if( ret != sizeof(CMonitor) )
    {

        log_history(0,0,"write (udp) Error [%s]",strerror(errno));
    }

    close(sock);
                      
                      
    newSockfd.close();
  //  delete info;

    //log_history(0,0,"socket write end");

    return NULL;
}


int configParse(char* file)
{
    Q_Entry *pEntry;
    int  i;
    int  nRetFlag = TRUE;
    char *pszTmp;
    CKSConfig conf;
//    memset(&gConf,0x00,sizeof(gConf));

    // read mert conf
    if((pEntry = conf.qfDecoder(file)) == NULL) {
        printf("WARNING: Configuration file(%s) not found.\n", file);
        return -1;
    }

    conf.strncpy2(gConf.domainName , conf.FetchEntry("domain.self"),64);
    if( gConf.domainName == NULL ) strcpy(gConf.domainName,"");


    return 0;
}


