#include "senderMMSProcess.h"
#include "Encrypt.h"
#include "ksbase64.h"
#include <sys/time.h>
static CMMSPacketSend mmsPacketSend;
static int S_PROCESS_NO;
static int proc_id;

int main(int argc,char* argv[])
{
	
	/*
	 * 1 : sockfd
	 * 2 : pipe
	 * 3 : version
	 * 4 : conf file
	 */
	int sockfd;
	int fd;
	int ret;
	char buff[SOCKET_BUFF];
	CLogonDbInfo logonDbInfo;
	CKSSocket db;
	CSenderDbMMSSEQ senderDbMMSSEQ;
	
	// P1. 클라이언트 소켓
	sockfd = atoi(argv[1]);
		
	
	// P2. 기준정보(고객정보, 연결정보, 프로세스정보, 로그파일정보 등)
	fd = atoi(argv[2]);
	memset(&logonDbInfo,0x00,sizeof(logonDbInfo));
	read(fd,(char*)&logonDbInfo,sizeof(logonDbInfo));
	close(fd);
	
		
	// P3. 로그파일 설정
	memset(_DATALOG,0x00,sizeof(_DATALOG));
	memset(_MONILOG,0x00,sizeof(_MONILOG));
	char* p;
	
	sprintf(_DATALOG,"%s/",logonDbInfo.szLogPath);
	sprintf(_MONILOG,"%s/",logonDbInfo.szLogPath);
	
	memset(szSenderID, 0x00, sizeof(szSenderID));
	strcpy(szSenderID, logonDbInfo.szCID);
	
	S_PROCESS_NO = getpid();
	
	p = strtok(logonDbInfo.szLogFilePath,"|");
	if( p )
	{
		strcat(_MONILOG, p);
	}
	else
	{
	  logPrintS(0,"[ERR] logondbinfo logfilepath failed - get monitor [%s]",logonDbInfo.szLogFilePath);
	  return -1;
	}
	
	p = strtok(NULL,"|");
	if( p )
	{
		strcat(_DATALOG, p);
	}
	else
	{
	  logPrintS(0,"[ERR] logondbinfo logfilepath failed - get data [%s]",logonDbInfo.szLogFilePath);
	  return -1;
	}
	
	logPrintS(0,"[INF] P01. 클라이언트 소켓 FD [%d]", sockfd);
	logPrintS(0,"[INF] P02. 기준정보 복사완료");
	logPrintS(0,"[INF] P03. 로그파일 설정 filepath - logfile[%s] monitorfile[%s] PID[%d]", _DATALOG, _MONILOG, S_PROCESS_NO);
	
	
	// P4. SENDER DB 설정
	char szSenderDBIP[16];
	char szSenderDBName[64];

	memset(szSenderDBIP, 0x00, sizeof(szSenderDBIP));
	memset(szSenderDBName, 0x00, sizeof(szSenderDBName));
	
	strcpy(szSenderDBName, logonDbInfo.szSenderDBName);
	
	p = strtok(szSenderDBName,":");
	if( p )
	{
	    strcpy(szSenderDBIP, p);
	}
	else
	{
	    logPrintS(0,"[ERR] logondbinfo senderDBName failed - [%s]",logonDbInfo.szSenderDBName);
	    return -1;
	}
	
	sprintf(senderDbDomainName,"%s/", logonDbInfo.szDomainPath);
	
	p = strtok(NULL,":");
	if( p )
	{
	    strcat(senderDbDomainName, p);
	}
	else
	{
	    logPrintS(0,"[ERR] logondbinfo senderDbDomainName failed - [%s]",logonDbInfo.szSenderDBName);
	    return -1;
	}
	
	logPrintS(0,"[INF] P04. SENDER DB 설정 senderDomainDBName - [%s]", senderDbDomainName);
	
	
	// P5. SENDER 구성정보파일 파싱
	ret = configParse(argv[4]);
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] configParse Failed");
    exit(1);
	}
	
	logPrintS(0,"[INF] P05. SENDER 구성정보파일 파싱 config file - logonDBName [%s]", gConf.logonDBName);
	logPrintS(0,"[INF] P05. SENDER 구성정보파일 파싱 config file - monitorName [%s]", gConf.monitorName);
	logPrintS(0,"[INF] P05. SENDER 구성정보파일 파싱 config file - domainPath [%s]", gConf.domainPath);
	logPrintS(0,"[INF] P05. SENDER 구성정보파일 파싱 config file - socketLinkTimeOut [%d]", gConf.socketLinkTimeOut);
	logPrintS(0,"[INF] P05. SENDER 구성정보파일 파싱 config file - dbRequestTimeOut [%d]", gConf.dbRequestTimeOut);
	logPrintS(0,"[INF] P05. SENDER 구성정보파일 파싱 config file - ContentPath [%s]", gConf.ContentPath);
	logPrintS(0,"[INF] P05. SENDER 구성정보파일 파싱 config file - mmdIdHeader [%d]", gConf.dbMmsIdHeader);
	
	ret = getMMSIDSEQ(db, senderDbMMSSEQ);
	
	if( ret < 0 )
	{
		logPrintS(0,"[ERR] db select get seq failed");
		//return ret; 
	}
	
	proc_id = senderDbMMSSEQ.seq;
	
	if( proc_id == -1 )
		proc_id = 9999;
	
	// P6. SENDER 프로세스 실행
	SenderProcess *mSenderProcess = new SenderProcess();
	
	logPrintS(0,"[INF] P06. SENDER 프로세스 실행");
		
	mSenderProcess->SenderMain(sockfd, logonDbInfo);
	
	logPrintS(0,"[INF] P07. SENDER 프로세스 종료");
		
	return 0;
}

//ACK 전송
int sendAck(CKSSocket& hRemoteSock,CMMSPacketSend& mmsPacketSend, int nCode,int ctnid,string strDesc)
{
	int ret=0;
	string strPacket;
	strPacket = "";
	strPacket.reserve(0);
	string key;
	char szCode[8];

    //SA1. ACK 세팅
	memset(szCode ,0x00, sizeof(szCode));

	sprintf(szCode,"%d",nCode);

	key = mmsPacketSend.getKeyValue();

	strPacket = "BEGIN ACK\r\nKEY:" + key + "\r\nCODE:" ;
	strPacket += szCode;
	strPacket += "\r\nDESC:" + strDesc + "\r\nEND\r\n";
		
	//SA2. socket ack
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	logPrintS(1,"[INF] SND ACK key[%s]code[%s]strDesc[%s]", key.c_str(), szCode, strDesc.c_str() );
	
	return ret;
}

//PONG 전송
int sendPong(CKSSocket& hRemoteSock)
{
	string strPacket;
	string strKey;
	CMMSPacketBase packetBase;
	int ret;
	
	//SP1. PONG 세팅
	packetBase.findValue((char*)hRemoteSock.getMsg(),"KEY",strKey);

	strPacket = "BEGIN PONG\r\nKEY:" + strKey + "\r\nEND\r\n";
	
	//SP2. PONG 전송
	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	
	if( ret != strPacket.length() )
	{
		logPrintS(0,"[ERR] SP2. PONG 전송 socket ack send failed sendSize/packetSize[%d/%d]",ret,strPacket.length());
		return ret;
	}
	//logPrintS(0,"[INF] SP2. PONG 전송 socket link PONG send");
  
	fflush(stdout);
	return 0;
}

/** @return 음수 일시 프로세스 종료*/
int recvLink(CKSSocket& hRemoteSock,char* buff)
{
	int ret;

	TypeMsgDataAck* pLinkAck = (TypeMsgDataAck*)buff;
	memset(pLinkAck->header.msgType, 0x00, sizeof(pLinkAck->header.msgType));
	strcpy(pLinkAck->header.msgType,"8");

	ret = hRemoteSock.send(buff,sizeof(TypeMsgDataAck));
	if( ret != sizeof(TypeMsgDataAck))
	{
		logPrintS(0,"[ERR] socket link ack send failed - errno[%s] sendSize/packetSize[%d/%d]", strerror(errno), ret, sizeof(TypeMsgDataAck));
		return -1;
	}
	time(&SLastTLink);

	return 0;
}

//로그기록
void logPrintS(int type, const char *format, ...)
{
	va_list args;
	char logMsg[SOCKET_BUFF];
	char tmpMsg[SOCKET_BUFF];

	va_start(args, format);
	vsprintf(tmpMsg, format, args);
	va_end(args);

	sprintf(logMsg,"[S][%s] %s",szSenderID,tmpMsg);
	if (type==1)
	{
		_logPrint(_DATALOG,logMsg);
	}
	else
	{
		_monPrint(_MONILOG,logMsg);
	}
}

int send2DB(CKSSocket& db,CSenderDbInfo& senderDbInfo,CSenderDbInfoAck& ack)
{
	int ret;
	time_t ThisT,LastT;
	time(&LastT);

SEND2DB:
	time(&ThisT);
	if( (ret = (int)difftime(ThisT,LastT)) > gConf.dbRequestTimeOut )
	{
		logPrintS(0,"[ERR] socket_domain send2DB timeout - [%d]sec ret[%d]sec", gConf.dbRequestTimeOut, ret);
		db.close();

		return -1;
	}

	ret = db.connectDomain(senderDbDomainName);
  
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] socket_domain send2DB connect failed - errno[%s]",strerror(ret));
		wait_a_moment(900000);
		goto SEND2DB;
	}

	ret = db.send((char*)&senderDbInfo,sizeof(CSenderDbInfo));

	if( ret != sizeof(CSenderDbInfo) )
	{
		logPrintS(0,"[ERR] socket_domain send2DB send failed - errno[%s] ret[%d]", strerror(errno), ret);
		db.close();
		wait_a_moment(900000);
		goto SEND2DB;
	}

#ifdef TIME
    /* gettimeofday */
    struct timeval timefirst, timesecond;
    struct timezone tzp;
    int secBuf, microsecBuf;
    float timeBuf;

    gettimeofday(&timefirst,&tzp);
    /* gettimeofday */
#endif

	ret = db.select(gConf.dbRequestTimeOut,0);
	
	if( ret == 0 )
	{
		logPrintS(0,"[ERR] socket_domain send2DB recv timeout - config file dbRequest TimeOut [%d]sec", gConf.dbRequestTimeOut);
		db.close();
		wait_a_moment(900000);
		goto SEND2DB;
	}

	if( ret < 0 )
	{
		logPrintS(0,"[ERR] socket_domain send2DB select failed - errno[%s]",strerror(errno));
		db.close();
		wait_a_moment(900000);
		goto SEND2DB;
	}

#ifdef TIME
    gettimeofday(&timesecond,&tzp);

    secBuf = (timesecond.tv_sec - timefirst.tv_sec);
    microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
    timeBuf = microsecBuf;
    timeBuf = timeBuf / 1000000;
    timeBuf = timeBuf + secBuf;
    logPrintS(0,"send2DB time [%f]",timeBuf);
#endif

	ret = db.rcvmsg((char*)&ack);
	
	if( ret == 0 || ret < 0 )
	{
		logPrintS(0,"senderDB recv Error [%s]ret[%d]",strerror(errno),ret);
		db.close();
		wait_a_moment(900000);
		goto SEND2DB;
	}
	db.close();

	return 0;
}

//CONFIG파일 파싱
int configParse(char* file)
{
	Q_Entry *pEntry;
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	CKSConfig conf;

    //CP1. 파싱파일 존재
	if((pEntry = conf.qfDecoder(file)) == NULL) 
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}

	conf.strncpy2(gConf.logonDBName , conf.FetchEntry("domain.logondb"),64);
	if( gConf.logonDBName == NULL )
	{
		strcpy(gConf.logonDBName,"");
	}
	
	conf.strncpy2(gConf.monitorName , conf.FetchEntry("domain.monitor"),64);
	
	if( gConf.monitorName == NULL )
	{
		strcpy(gConf.monitorName,"");
	}

	conf.strncpy2(gConf.domainPath , conf.FetchEntry("domain.self"),64);
	if( gConf.domainPath == NULL )
	{
		strcpy(gConf.domainPath,"");
	}

	conf.strncpy2(gConf.ContentPath , conf.FetchEntry("path.mmscontent"),64);
	
	if( gConf.ContentPath == NULL )
	{
		strcpy(gConf.ContentPath,"");
	}

	gConf.socketLinkTimeOut = conf.FetchEntryInt("socket.linktimeout");
	
	if( gConf.socketLinkTimeOut <= 1 )
	{
		gConf.socketLinkTimeOut = 2;
	}

	gConf.dbRequestTimeOut = conf.FetchEntryInt("db.requesttimeout");

	if( gConf.dbRequestTimeOut <= 0 )
	{
		gConf.dbRequestTimeOut = 1;
	}
	
	gConf.dbMmsIdHeader = conf.FetchEntryInt("db.mmsidhead");

	if( gConf.dbMmsIdHeader <= 0 )
	{
		gConf.dbMmsIdHeader = 0;
	}

	return 0;
}

//사용X
void viewPackSender(char *a,int n)
{
	int i;
	char logMsg[VIEWPACK_MAX_SIZE];
	char strtmp[VIEWPACK_MAX_SIZE];
	
	memset(logMsg,0x00, sizeof logMsg);
	memset(strtmp,0x00, sizeof strtmp);
	
	for(i=0;i<n;i++)
	{
		if( a[i] == 0x00 )
		{
			strtmp[i] = '.';
	    }
	    else
		{
			memcpy(strtmp+i,a+i,1);
		}
	}
	
	sprintf(logMsg,"info:[%s]",strtmp);
	_monPrint(_MONILOG,logMsg);
	
	return ;
}

/*
int setMMSMSG2DB_TALK(CKSSocket& db, long long nMMSId, CMMSPacketSend& mmsPacketSend, int priority)
{
	int ret, size;
	time_t ThisT,LastT;
	char szType[50+1];
    char* pData = 0;
    CMData mData;
	//CSenderDbMMSMSG senderDbMMSMSG;
	CSenderDbMMSMSG_TALK senderDbMMSMSG;
	time(&LastT);
	
	pData = (char*)malloc(sizeof(pData)*sizeof(char));
	//SMG1. mData 가져오기
	ret = mmsPacketSend.getMDataFirst(mData);
    if( ret != 0 )
	{
        logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}
	
	//SMG2. mData 복호화
	memset(szType, 0x00, sizeof(szType));
    sprintf(szType,(char*)mData.contentType.strType.c_str());
    if (strcmp(szType, "TXT") == 0)
    { 
    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
    }
    else
    {
    	while((  ret = mmsPacketSend.getMDataNext(mData)) == 0 )
	    {
	        memset(szType,0x00,sizeof(szType));	
	        sprintf(szType,(char*)mData.contentType.strType.c_str());
		    if (strcmp(szType, "TXT") == 0)
		    {
		    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
		    	break;
		    }
	    }
    }
	
	//SMG3. senderDbMMSMSG에 데이타 세팅
	//memset(&senderDbMMSMSG,0x00,sizeof(CSenderDbMMSMSG));
	memset(&senderDbMMSMSG, 0x00, sizeof(CSenderDbMMSMSG_TALK));
	//암호화 체크
	if(strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), " aes_base64", 11) == 0) 
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key();
		en.decrypt(receiverNum, receiverNum, strlen((char*)receiverNum));
		sprintf(senderDbMMSMSG.szDstAddr ,"82%s", string((char*)receiverNum).substr(1).c_str());
		//logPrintS(1,"[INFO] [%s]", senderDbMMSMSG.szDstAddr);
	
		//메시지 암호화 풀기	
		en.decrypt((unsigned char*)pData, (unsigned char*)pData, atoi(mmsPacketSend.getMsgOrgSizeValue()));
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), (char*)pData);
		strncpy(senderDbMMSMSG.szMsgBody, (char*)pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
		//logPrintS(1,"[INFO][%d]szMsgBody[%s]", atoi(mmsPacketSend.getMsgOrgSizeValue()), (char*)pData);
	}
	else
	{
		sprintf(senderDbMMSMSG.szDstAddr ,"82%s", mmsPacketSend.getReceiverValue()+1);
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), pData);
		strncpy(senderDbMMSMSG.szMsgBody, pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
	}
	
	//sprintf(senderDbMMSMSG.szDstAddr ,"82%s", mmsPacketSend.getReceiverValue()+1);
	//snprintf(senderDbMMSMSG.szMsgBody, mData.strData.length(), mData.strData.c_str());
	
	
	senderDbMMSMSG.header.type = SETSENDQUE_V2;
	senderDbMMSMSG.header.leng = sizeof(CSenderDbMMSMSG_TALK) - sizeof(Header);
	
	//sprintf(senderDbMMSMSG.szQName,"%s",getTelcoQName(mmsPacketSend.getReceiverValue()));
	//큐 NUMBER 저장
	sprintf(senderDbMMSMSG.szQName,"%d", 
			getTelcoId(mmsPacketSend.getImgCnt(), gSenderInfo.szSmsTelcoInfo, gSenderInfo.szQType));
	
	//senderDbMMSMSG.nPriority = priority;
	//senderDbMMSMSG.nCtnId = ctnid;

	strcpy(senderDbMMSMSG.szSenderKey ,mmsPacketSend.getSenderKeyValue());
	snprintf(senderDbMMSMSG.szTmplCd, sizeof(senderDbMMSMSG.szTmplCd), mmsPacketSend.getTmplCdValue());
	
	//배포Agent 중 btname, bturl 이 string 으로 "null" 로 오는 것이 있어서 예외 처리(고대병원)
	if(strncmp(mmsPacketSend.getBtNameValue(), "null", 4) == 0)
	{
		snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), "");
		snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), "");
	}
	else
	{
		snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), mmsPacketSend.getBtNameValue());
		snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), mmsPacketSend.getBtUrlValue());
	}

	snprintf(senderDbMMSMSG.szResMethod, sizeof(senderDbMMSMSG.szResMethod), gSenderInfo.szResMethod);
	snprintf(senderDbMMSMSG.szTimeout, sizeof(senderDbMMSMSG.szTimeout), gSenderInfo.szTimeout);

	senderDbMMSMSG.nMMSId = nMMSId;
	
	CSenderDbInfoAck senderDbInfoAck;
	CDBUtil dbUtil;
	
	free(pData);
	
//	dbUtil.sendQuery(db,(void*)&senderDbMMSMSG,(void*)&senderDbInfoAck,gConf.dbRequestTimeOut,senderDbDomainName);
    //SMG4. 데이타 senderMMSDB로 전송
	dbUtil.sendQuery(db,(void*)&senderDbMMSMSG,(void*)&senderDbInfoAck,gConf.dbRequestTimeOut,senderDbDomainName,ret);

	return ret;
}*/

int setMMSMSG2DB_ATK_V3(CKSSocket& db, long long nMMSId, CMMSPacketSend& mmsPacketSend, int priority)
{
	int ret, size;
	time_t ThisT,LastT;
	char szType[50+1];
	char* pData = 0;
    CMData mData;
	//CSenderDbMMSMSG senderDbMMSMSG;
	CSenderDbMMSMSG_TALK senderDbMMSMSG;
	time(&LastT);
	
	
	//SMG1. mData 가져오기
	ret = mmsPacketSend.getMDataFirst(mData);
    if( ret != 0 )
	{
        logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}
	
	//SMG2. mData 복호화
	memset(szType, 0x00, sizeof(szType));
    sprintf(szType,(char*)mData.contentType.strType.c_str());
    if (strcmp(szType, "TXT") == 0)
    { 
    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
    }
    else
    {
    	while((  ret = mmsPacketSend.getMDataNext(mData)) == 0 )
	    {
	        memset(szType,0x00,sizeof(szType));	
	        sprintf(szType,(char*)mData.contentType.strType.c_str());
		    if (strcmp(szType, "TXT") == 0)
		    {
		    	pData = (char*)__base64_decode((unsigned char *)mData.strData.c_str(), mData.strData.length(), &size);
		    	break;
		    }
	    }
    }
	
	//SMG3. senderDbMMSMSG에 데이타 세팅
	//memset(&senderDbMMSMSG,0x00,sizeof(CSenderDbMMSMSG));
	memset(&senderDbMMSMSG, 0x00, sizeof(CSenderDbMMSMSG_TALK));
	//암호화 체크
	if(strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), " aes_base64", 11) == 0) 
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key();
		en.decrypt(receiverNum, receiverNum, strlen((char*)receiverNum));
		if(strlen((char*)receiverNum)> 0)
		{
			sprintf(senderDbMMSMSG.szDstAddr,"82%s", string((char*)receiverNum).substr(1).c_str());
		}else{
			sprintf(senderDbMMSMSG.szDstAddr,"82%s", "");
		}
		//snprintf(senderDbMMSMSG.szDstAddr, sizeof(senderDbMMSMSG.szDstAddr)-1,"82%s", string((char*)receiverNum).substr(1).c_str());
		//logPrintS(1,"[INFO] [%s]", senderDbMMSMSG.szDstAddr);
	
		//메시지 암호화 풀기	
		en.decrypt((unsigned char*)pData, (unsigned char*)pData, atoi(mmsPacketSend.getMsgOrgSizeValue()));
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), (char*)pData);
		strncpy(senderDbMMSMSG.szMsgBody, (char*)pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
		//logPrintS(1,"[INFO][%d]szMsgBody[%s]", atoi(mmsPacketSend.getMsgOrgSizeValue()), (char*)pData);
		free(receiverNum);
	}
	else
	{
		//sprintf(senderDbMMSMSG.szDstAddr ,"82%s", mmsPacketSend.getReceiverValue()+1);
		snprintf(senderDbMMSMSG.szDstAddr,sizeof(senderDbMMSMSG.szDstAddr)-1 ,"82%s", mmsPacketSend.getReceiverValue()+1);
		//snprintf(senderDbMMSMSG.szMsgBody, sizeof(senderDbMMSMSG.szMsgBody), pData);
		strncpy(senderDbMMSMSG.szMsgBody, pData, sizeof(senderDbMMSMSG.szMsgBody)-1);
	}
	
	//sprintf(senderDbMMSMSG.szDstAddr ,"82%s", mmsPacketSend.getReceiverValue()+1);
	//snprintf(senderDbMMSMSG.szMsgBody, mData.strData.length(), mData.strData.c_str());
	
	
	senderDbMMSMSG.header.type = SETSENDQUE_V4;
	senderDbMMSMSG.header.leng = sizeof(CSenderDbMMSMSG_TALK) - sizeof(Header);
	
	//sprintf(senderDbMMSMSG.szQName,"%s",getTelcoQName(mmsPacketSend.getReceiverValue()));
	//큐 NUMBER 저장
	sprintf(senderDbMMSMSG.szQName,"%d", 
			getTelcoId(mmsPacketSend.getImgCnt(), gSenderInfo.szSmsTelcoInfo, gSenderInfo.szQType));
	
	//20180829 우선순위 적용
	senderDbMMSMSG.nPriority = priority; 
	//senderDbMMSMSG.nCtnId = ctnid;

	//strcpy(senderDbMMSMSG.szSenderKey ,mmsPacketSend.getSenderKeyValue());
	strncpy(senderDbMMSMSG.szSenderKey ,mmsPacketSend.getSenderKeyValue(),sizeof(senderDbMMSMSG.szSenderKey)-1);
	snprintf(senderDbMMSMSG.szTmplCd, sizeof(senderDbMMSMSG.szTmplCd), mmsPacketSend.getTmplCdValue());
	
	//배포Agent 중 btname, bturl 이 string 으로 "null" 로 오는 것이 있어서 예외 처리(고대병원)
	if(strncmp(mmsPacketSend.getBtNameValue(), "null", 4) == 0)
	{
		snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), "");
		snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), "");
	}
	else
	{
		snprintf(senderDbMMSMSG.szBtName, sizeof(senderDbMMSMSG.szBtName), mmsPacketSend.getBtNameValue());
		//snprintf(senderDbMMSMSG.szBtUrl, sizeof(senderDbMMSMSG.szBtUrl), mmsPacketSend.getBtUrlValue());
		strncpy(senderDbMMSMSG.szBtUrl,  mmsPacketSend.getBtUrlValue(),sizeof(senderDbMMSMSG.szBtUrl)-1);
	}

	if(strlen(mmsPacketSend.getAttachmentValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szAttachment, mmsPacketSend.getAttachmentValue(),sizeof(senderDbMMSMSG.szAttachment)-1);
	}
	else 
	{
		//snprintf(senderDbMMSMSG.szButton, sizeof(senderDbMMSMSG.szButton), mmsPacketSend.getButtonValue());
		strncpy(senderDbMMSMSG.szButton, mmsPacketSend.getButtonValue(),sizeof(senderDbMMSMSG.szButton)-1);
	}
	
	if(strlen(mmsPacketSend.getSupplementValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szSupplement, mmsPacketSend.getSupplementValue(),sizeof(senderDbMMSMSG.szSupplement)-1);
	}
	
	if(strlen(mmsPacketSend.getKkoHeaderValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szKkoHeader, mmsPacketSend.getKkoHeaderValue(),sizeof(senderDbMMSMSG.szKkoHeader)-1);
	}
	
	if(strlen(mmsPacketSend.getMessageTypeValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szMessageType, mmsPacketSend.getMessageTypeValue(),sizeof(senderDbMMSMSG.szMessageType)-1);
	}
	
	/*snprintf(senderDbMMSMSG.szResMethod, sizeof(senderDbMMSMSG.szResMethod), gSenderInfo.szResMethod);
	snprintf(senderDbMMSMSG.szTimeout, sizeof(senderDbMMSMSG.szTimeout), gSenderInfo.szTimeout);*/
	
	if(strlen(mmsPacketSend.getMethodValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szResMethod,  mmsPacketSend.getMethodValue(),sizeof(senderDbMMSMSG.szResMethod)-1);
	}else{
		snprintf(senderDbMMSMSG.szResMethod, sizeof(senderDbMMSMSG.szResMethod), gSenderInfo.szResMethod);
	}
	
	if(strlen(mmsPacketSend.getTimeoutValue()) > 0)
	{
		strncpy(senderDbMMSMSG.szTimeout,  mmsPacketSend.getTimeoutValue(),sizeof(senderDbMMSMSG.szTimeout)-1);
	}else{
		snprintf(senderDbMMSMSG.szTimeout, sizeof(senderDbMMSMSG.szTimeout), gSenderInfo.szTimeout);
	}
	
	strncpy(senderDbMMSMSG.szTitle, mmsPacketSend.getTitleValue(),sizeof(senderDbMMSMSG.szTitle)-1);

	senderDbMMSMSG.nMMSId = nMMSId;
	
	strncpy(senderDbMMSMSG.szPrice, mmsPacketSend.getPriceValue(),sizeof(senderDbMMSMSG.szPrice)-1);
	strncpy(senderDbMMSMSG.szCurType, mmsPacketSend.getCurTypeValue(),sizeof(senderDbMMSMSG.szCurType)-1);



	
	CSenderDbInfoAck senderDbInfoAck;
	CDBUtil dbUtil;
	
	free(pData);
//	dbUtil.sendQuery(db,(void*)&senderDbMMSMSG,(void*)&senderDbInfoAck,gConf.dbRequestTimeOut,senderDbDomainName);
    //SMG4. 데이타 senderMMSDB로 전송
	dbUtil.sendQuery(db,(void*)&senderDbMMSMSG,(void*)&senderDbInfoAck,gConf.dbRequestTimeOut,senderDbDomainName,ret);

	return ret;
}

//20140212 ADD return case
// 20140224 mmsid type : unsigned long long
//int setMMSTBL2DB(CKSSocket& db,int nMMSId, int ctnid,int priority,CBcastData * pBcastData)
int setMMSTBL2DB(CKSSocket& db,long long nMMSId, int ctnid,int priority,CBcastData * pBcastData)
{
	int ret;
	time_t ThisT,LastT;
	CSenderDbMMSTBL senderDbMMSTBL;
	CMData mData;
	time(&LastT);
	
	//SMT1. mmsPacketSend.getMDataFirst
	ret = mmsPacketSend.getMDataFirst(mData);
	if( ret != 0 )
	{
		logPrintS(1,"[%s] SMT1. mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}
	
	//SMT2. senderDbMMSTBL에 데이타 세팅
	memset(&senderDbMMSTBL,0x00,sizeof(CSenderDbMMSTBL));

	senderDbMMSTBL.header.type = SETSENDTBL;
	senderDbMMSTBL.header.leng = sizeof(CSenderDbMMSTBL) - sizeof(Header);
	
	if (strlen(mmsPacketSend.getSenderValue()) <= 0)
		strcpy(senderDbMMSTBL.szCallBack, "0");
	else
		strcpy(senderDbMMSTBL.szCallBack, mmsPacketSend.getSenderValue());

	if(strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), " aes_base64", 10) == 0)
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key();
		//logPrintS(1,"receiverNum:%d", atoi(mmsPacketSend.getPhoneSizeValue()));
		en.decrypt(receiverNum, receiverNum, atoi(mmsPacketSend.getPhoneSizeValue()));
		strcpy(senderDbMMSTBL.szDstAddr, (char*)receiverNum);
		free(receiverNum);
	}
	else
	{
		strcpy(senderDbMMSTBL.szDstAddr , mmsPacketSend.getReceiverValue());  
	}
	
	strcpy(senderDbMMSTBL.szMsgTitle,mmsPacketSend.getSubjectValue());
	strcpy(senderDbMMSTBL.szPtnSn,mmsPacketSend.getKeyValue());
	strcpy(senderDbMMSTBL.szResvData,mmsPacketSend.getExtendValue());
	strcpy(senderDbMMSTBL.szCid,szSenderID);
	senderDbMMSTBL.nMsgType 	= 1;	/* 0: LMS|MMS, 1: Alim Talk */
	senderDbMMSTBL.nPriority 	= priority;
	senderDbMMSTBL.nCtnId 		= ctnid;
	senderDbMMSTBL.nCtnType 	= mmsPacketSend.getCtnType();
	senderDbMMSTBL.nRgnRate 	= 65;
	senderDbMMSTBL.nInterval 	= 10;
	senderDbMMSTBL.nTextCnt 	= mmsPacketSend.getTextCnt();
	senderDbMMSTBL.nImgCnt 		= mmsPacketSend.getImgCnt();
	senderDbMMSTBL.nAugCnt 		= mmsPacketSend.getAugCnt();
	senderDbMMSTBL.nMpCnt 		= mmsPacketSend.getMpCnt();
	senderDbMMSTBL.nMMSId 		= nMMSId;
	
	//20190708 senderkey추가
	strncpy(senderDbMMSTBL.szSenderKey ,mmsPacketSend.getSenderKeyValue(),sizeof(senderDbMMSTBL.szSenderKey)-1);
	

	CSenderDbInfoAck senderDbInfoAck;
	CDBUtil dbUtil;

	//SMT3. 데이타 senderMMSDB로 전송
	dbUtil.sendQuery(db, (void*)&senderDbMMSTBL,(void*)&senderDbInfoAck,gConf.dbRequestTimeOut,senderDbDomainName,ret);

	return ret;
}

/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
//int getCTNID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID,char* cid)
int getCTNID2DB(CSenderDbMMSID& senderDbMMSID)
{
	senderDbMMSID.ctnid = -1;
	return 0;
}

/**====================================================================================**/
// 20161116 make mmsid
/**====================================================================================**/
long long mkMMSID(int mmsIdHeader)
{
	char	pch[30];
	char	pchlast[30];
	
	time_t	the_time;
	struct	timeval val;
	struct	tm	*tm_ptr;
	
	long long ulltm;
	
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);

	memset(pch				,0x00		,sizeof(pch));	
	sprintf(pch, "%2d%02d%02d%02d%09d"
				,tp.tm_mday+10
				,tp.tm_hour
				,tp.tm_min
				,tp.tm_sec
				,(int)tmv.tv_nsec
				);

		
	memset(pchlast		,0x00		,sizeof(pchlast));
	sprintf(pchlast,"%.12s%04d", pch, proc_id);
	
	ulltm = atoll(pchlast);

	//logPrintS(1,"[INF] mkmmsid pch[%s] pchlast[%s] ulltmid[%lld]", pch, pchlast, ulltm );

	return ulltm;
}


/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: mmsid get fail
 **	 1: ok
 **/
/**====================================================================================**/
//int getMMSID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID, char* cid)
int getMMSID2DB(CKSSocket& db,CSenderDbMMSID& senderDbMMSID, char* cid)
{
	int ret = 0;
	CSenderDbInfoAck senderDbInfoAck;
	CDBUtil dbUtil;
	
	//GMI 1. senderDbMMSID에 데이타 세팅
	senderDbMMSID.header.type 	= GETMMSID;
	senderDbMMSID.header.leng 	= sizeof(CSenderDbMMSID) - sizeof(Header);
	senderDbMMSID.mmsid 		= 0;
	memcpy(senderDbMMSID.szCid,cid,10);
	senderDbInfoAck.mmsid = 0;
	
	//GMI 2. mmsid 얻기
    dbUtil.sendQuery(db,(void*)&senderDbMMSID,(void*)&senderDbInfoAck,gConf.dbRequestTimeOut,senderDbDomainName,ret);

    //senderDbInfoAck.mmsid = mkMMSID(gConf.dbMmsIdHeader);
	
	//senderDbInfoAck.mmsid = 9999999999999999;
	senderDbMMSID.mmsid = senderDbInfoAck.mmsid;

	/**====================================================================================**/
	// 20140212 : MOD if div
	/**====================================================================================**/
	//if(senderDbInfoAck.mmsid == 0 || senderDbInfoAck.mmsid < 0)
	if(senderDbInfoAck.mmsid == 0 || senderDbInfoAck.mmsid < 0)
	{
		//logPrintS(1,"[ERR] socket_domain get mms id to db failed - mmsid[%lld] dbutil_getErrMsg[%s]", senderDbMMSID.mmsid, dbUtil.getErrorMsg());
		logPrintS(1,"[ERR] socket_domain get mms id to db failed - mmsid[%lld]", senderDbMMSID.mmsid);
		// 2013.12 LSY 보완 필요
		//senderDbMMSID.mmsid = 999999999;
		senderDbInfoAck.mmsid = mkMMSID(gConf.dbMmsIdHeader);
		
		senderDbMMSID.mmsid = senderDbInfoAck.mmsid;
				
		//return 0;	// 20140212 : ADD return
		if(senderDbInfoAck.mmsid == 0 || senderDbInfoAck.mmsid < 0)
		return -1;
	}

	if( senderDbInfoAck.mmsid < 0 )
	{
		logPrintS(1,"[ERR] socket_domain get mms id mkMMSID failed - mmsid[%lld]", senderDbMMSID.mmsid);
		
		return -1;
	}

	return 1;
	/**====================================================================================**/
}

/**====================================================================================**/
/** 20140212 ADD RETURN CASE
 **	return value
 **	-1: fail
 **	 0: seq get fail
 **	 1: ok
 **/
/**====================================================================================**/
//int getMMSIDSEQ(CKSSocket& db, CSenderDbMMSID& senderDbMMSID, char* cid)
int getMMSIDSEQ(CKSSocket& db,CSenderDbMMSSEQ& senderDbMMSSEQ)
{
	int ret = 0;
	CSenderDbInfoAck senderDbInfoAck;
	CDBUtil dbUtil;
	
	//GMI 1. senderDbMMSID에 데이타 세팅
	senderDbMMSSEQ.header.type 	= GETMMSSEQ;
	senderDbMMSSEQ.header.leng 	= sizeof(senderDbMMSSEQ) - sizeof(Header);
	
	senderDbInfoAck.seq = 0;
	
	//GMI 2. mmsid 얻기
    dbUtil.sendQuery(db,(void*)&senderDbMMSSEQ,(void*)&senderDbInfoAck,gConf.dbRequestTimeOut,senderDbDomainName,ret);

    //senderDbInfoAck.mmsid = mkMMSID(gConf.dbMmsIdHeader);
	
	//senderDbInfoAck.mmsid = 9999999999999999;
	senderDbMMSSEQ.seq = senderDbInfoAck.seq;

	/**====================================================================================**/
	// 20140212 : MOD if div
	/**====================================================================================**/
	if(senderDbInfoAck.seq == 0 || senderDbInfoAck.seq < 0)
	{
		//logPrintS(1,"[ERR] socket_domain get mms id to db failed - mmsid[%lld] dbutil_getErrMsg[%s]", senderDbMMSID.mmsid, dbUtil.getErrorMsg());
		logPrintS(1,"[ERR] socket_domain get seq to db failed - seq[%d]", senderDbMMSSEQ.seq);
		// 2013.12 LSY 보완 필요
		senderDbMMSSEQ.seq = 9999;
				
		return 0;	
	}
	
	return 1;
	/**====================================================================================**/
}

//사용X
int setRPTTBL2DB(CKSSocket& db, long long nMMSId, int ctnid,int priority, int _code, char *code_text)
{
	int ret;
	time_t ThisT,LastT;
	CSenderDbMMSRPTTBL senderDbRPTTBL;
	time(&LastT);
    
    //P26-1. senderDbRPTTBL에 데이타 세팅
	memset(&senderDbRPTTBL, 0x00, sizeof(CSenderDbMMSRPTTBL));

	senderDbRPTTBL.header.type = SETRPTTBL;
	senderDbRPTTBL.header.leng = sizeof(CSenderDbMMSRPTTBL) - sizeof(Header);
	
	senderDbRPTTBL.nMMSId = nMMSId;
	sprintf(senderDbRPTTBL.szCallBack, mmsPacketSend.getSenderValue());
	sprintf(senderDbRPTTBL.szDstAddr,mmsPacketSend.getReceiverValue());

	senderDbRPTTBL.res_code = _code;
	sprintf(senderDbRPTTBL.res_text, code_text);

	CSenderDbInfoAck senderDbInfoAck;
	CDBUtil dbUtil;

    //P26-2. 데이타 senderMMSDB로 전송
	dbUtil.sendQuery(db, (void*)&senderDbRPTTBL,(void*)&senderDbInfoAck, gConf.dbRequestTimeOut, senderDbDomainName, ret);

	return ret;
}



int getTelcoId(int imgCnt, char* szTelco, int nColorYN)
{
	char* p;
	int telcoArray[4];
	int i=0;
	
	memset(telcoArray,0x00,sizeof(telcoArray));

	p = strtok(szTelco,"|");
	
	if( p == NULL )
	{
		return 1;
	}
	telcoArray[i++] = atoi(p);

	while(p = strtok(NULL,"|") )
	{
		telcoArray[i++]= atoi(p);
		if( i >= 4 )
		{
			break;
		}
	}

	
	//szTelco 다시 복구.버그 수정.2012.12.10.
	/*
	sprintf(szTelco, "%d|%d|%d", telcoArray[0], telcoArray[1], telcoArray[2]);

	//컬러 문자를 우선으로 통신사를 체크한다. (컬러 LMS/MMS)
	if (nColorYN == 1)
	{
		return telcoArray[2];			//컬러
	}
	else if (imgCnt == 0)				//일반 LMS
	{
		return telcoArray[0];			//LMS
	}
	else if (imgCnt > 0)				//일반 MMS
	{
		return telcoArray[1];			//MMS
	}

	return 1;
	*/
	sprintf(szTelco, "%d|%d|%d|%d", telcoArray[0], telcoArray[1], telcoArray[2], telcoArray[3]);
	return telcoArray[3];
}

int getTelcoId(int imgCnt, char* szTelco, char *szQType)
{
    char* p;
    int telcoArray[6];
    int i = 0;
        
    memset(telcoArray,0x00,sizeof(telcoArray));

    p = strtok(szTelco,"|");
    
    if( p == NULL )
    {
        return 1;
    }
    telcoArray[i++] = atoi(p);

    while(p = strtok(NULL,"|") )
    {
        telcoArray[i++]= atoi(p);
        if( i >= 6 )
        {
            break;
        }
    }
    
    sprintf(szTelco, "%d|%d|%d|%d|%d|%d", 
            telcoArray[0], telcoArray[1], telcoArray[2], telcoArray[3], telcoArray[4], telcoArray[5]);

    //실시간큐
    if(strncmp(szQType, "type_real", 9) == 0) 
    {
        return telcoArray[3];
    }
    //배치큐
    else if(strncmp(szQType, "type_bt", 7) == 0)
    {
        return telcoArray[5];
    }
    else
    {
        return telcoArray[3];
    }
}

void writeLogMMSData(CMMSPacketSend& mmsPacketSend,long long mmsid, int ctnid)
{
    //logPrintS(1,"[INF] send message mmsid[%lld]ctnid[%d]key[%s]extend[%s]subject[%s]dst[]call[%s]contentCnt[%s]psktyn[%s]pktfyn[%s]plgtyn[%s] txt[%s] img[%s] mp[%s] bcast[%s]",
    logPrintS(1,"[INF] SND MSG mmsid[%lld]ctnid[%d]key[%s]extend[%s]subject[%s]dst[]call[%s]",
            mmsid,
            ctnid,
            mmsPacketSend.getKeyValue(),
            mmsPacketSend.getExtendValue(),
            mmsPacketSend.getSubjectValue(),
         /*   mmsPacketSend.getReceiverValue(),*/
            mmsPacketSend.getSenderValue()
         /*
            mmsPacketSend.getContentCntValue(),
            mmsPacketSend.getPsktynValue(),
            mmsPacketSend.getPktfynValue(),
            mmsPacketSend.getPlgtynValue(),
            mmsPacketSend.getTextCntValue(),
            mmsPacketSend.getImgCntValue(),
            //mmsPacketSend.getAudCntValue(),
            mmsPacketSend.getMpCntValue(),
            mmsPacketSend.getBcastCntValue(),
            mmsPacketSend.getMsgOrgSizeValue()
            */
            );
}


SenderProcess::SenderProcess()
{
	bDayWarnCheck=false;
	bMonWarnCheck=false;
}


void SenderProcess::SenderMain(int sockfd,CLogonDbInfo& logonDbInfo)
{
	int ret;
	CLogonUtil util;
	CAdminUtil admin;
	CKSSocket db;
	CProcessInfo processInfo;
	CMonitor monitor;
	
	char szLogonMsg[2048];
	  
	memset(&processInfo,0x00,sizeof(processInfo));
	memset(&gSenderInfo,0x00,sizeof(gSenderInfo));
	  
	strcpy(processInfo.processName,logonDbInfo.szSenderName);
	get_timestring("%04d%02d%02d%02d%02d%02d",time(NULL),processInfo.startTime);
	
	sprintf(processInfo.szPid,"%d",getpid());
	strcpy(processInfo.logonDBName,gConf.logonDBName);
	
	//CSenderInfo에 값 세팅
	util.findValueParse(logonDbInfo.szReserve, "mms_tel", gSenderInfo.szSmsTelcoInfo);
	util.findValueParse(logonDbInfo.szReserve, "mms_yn",  gSenderInfo.szSmsFlag);
	util.findValueParse(logonDbInfo.szReserve, "res_method",  gSenderInfo.szResMethod);
	util.findValueParse(logonDbInfo.szReserve, "timeout",  gSenderInfo.szTimeout);
	util.findValueParse(logonDbInfo.szReserve, "q_type",  gSenderInfo.szQType);
	util.findValueParse(logonDbInfo.szReserve, "block_yn",  gSenderInfo.szBlockYN);

	strcpy(gSenderInfo.szUrlTelcoInfo, "0");
	strcpy(gSenderInfo.szUrlFlag, "0");
	
	// util.displayLogonDbInfo(logonDbInfo, _MONILOG);
	// logonDbInfo Display
	
	sprintf(szLogonMsg,"[INF] logonDbInfo Display szCID[%s] szSIP[%s] szAPPName[%s] szServerInfo[%s] nRptNoDataSleep[%d] szSenderName[%s] szReportName[%s] szSenderDBName[%s] szReportDBName[%s] szLogFilePath[%s] szReserve[%s] nmPID[%d] nmJOB[%d] nmPRT[%d] nmCNT[%d] nmRST[%s] nUrlJob[%d] nRptWait[%d] classify[%c] szIP[%s] errno[] szLogPath[%s] szDomainPath[%s] szLimitType[%s] szLimitFlag[%s] nDayWarnCnt[%d] nMonWarnCnt[%d] nDayLimitCnt[%d] nMonLimitCnt[%d] nDayAccCnt[%d] nMonAccCnt[%d] nCurAccCnt[%d] szDate[%s]"
			,logonDbInfo.szCID 
            ,logonDbInfo.szSIP
            ,logonDbInfo.szAPPName
            ,logonDbInfo.szServerInfo
        	,logonDbInfo.nRptNoDataSleep
        	,logonDbInfo.szSenderName
        	,logonDbInfo.szReportName
        	,logonDbInfo.szSenderDBName
        	,logonDbInfo.szReportDBName
        	,logonDbInfo.szLogFilePath
        	,logonDbInfo.szReserve
        	,logonDbInfo.nmPID
        	,logonDbInfo.nmJOB 
        	,logonDbInfo.nmPRT 
        	,logonDbInfo.nmCNT 
        	,logonDbInfo.nmRST
        	,logonDbInfo.nUrlJob
        	,logonDbInfo.nRptWait
			,logonDbInfo.classify
        	,logonDbInfo.szIP
        	//,logonDbInfo.errno
        	,logonDbInfo.szLogPath
        	,logonDbInfo.szDomainPath
        	,logonDbInfo.szLimitType
			,logonDbInfo.szLimitFlag
			,logonDbInfo.nDayWarnCnt
			,logonDbInfo.nMonWarnCnt
			,logonDbInfo.nDayLimitCnt
			,logonDbInfo.nMonLimitCnt
			,logonDbInfo.nDayAccCnt
			,logonDbInfo.nMonAccCnt
			,logonDbInfo.nCurAccCnt
			,logonDbInfo.szDate);
				
	logPrintS(0,szLogonMsg);
	   
	logPrintS(0,"[INF] send process sender main start sockfd[%d]CID[%s]processInfo.startTime[%s]pid[%s]logonDbInfo.Reserve[%s]senderInfo[%s]smsFlag[%s]UrlTelcoInfo[%s]UrlFlag[%s]ChkCallback[%s]szResMethod[%s]szTimeout[%s]szQType[%s]SenderKey[%s]BlockYN[%s]"
            ,sockfd
            ,logonDbInfo.szCID
            ,processInfo.startTime
            ,processInfo.szPid
            ,logonDbInfo.szReserve
            ,gSenderInfo.szSmsTelcoInfo
            ,gSenderInfo.szSmsFlag
            ,gSenderInfo.szUrlTelcoInfo
            ,gSenderInfo.szUrlFlag
			,gSenderInfo.szChkCallback
			,gSenderInfo.szResMethod
	        ,gSenderInfo.szTimeout
	        ,gSenderInfo.szQType
			,gSenderInfo.szSenderKey
			,gSenderInfo.szBlockYN
			);
	
	
	CKSSocket hRemoteSock;
	hRemoteSock.attach(sockfd);
   
	//admin_domain create
	ret = admin.createDomainID(logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);
	
	logPrintS(0,"[INF] P08. admin_domain create - CID[%s]classify[%c]domainPath[%s]",
	logonDbInfo.szCID,
	logonDbInfo.classify,
	gConf.domainPath );
	
	if( ret != 0 )
	{
		logPrintS(0,"[ERR] P08. socket_domain create failed - CID[%s]classify[%c]domain_path[%s]", logonDbInfo.szCID, logonDbInfo.classify, gConf.domainPath);
        goto SenderEND;
	}
	
	//SM4. monitor server init
	monitor.Init("logon7", "sender", processInfo.processName, logonDbInfo.szCID, logonDbInfo.nmPID, logonDbInfo.szIP);
	
	time(&SLastTLink);
	
	nCurAccCnt = 0;

	memset(szLimitTime, 0x00, sizeof(szLimitTime));
	get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), szLimitTime);	// 현재 날짜 구하기
	memset(szLimitCurTime, 0x00, sizeof(szLimitCurTime));
    
	while(bSActive)
	{
		//SM5. socket_domain packet check 
		wait_a_moment(logonDbInfo.nmCNT);
		ret = admin.checkPacket(processInfo,logonDbInfo,sum);// check admin packet
		
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] admin.checkPacket check failed - CID[%s]ErrMsg[%s]",logonDbInfo.szCID,admin.getErrMsg());
			goto SenderEND;
		}

		switch(ret) 
		{
			case 3: // end
				bSActive = false;
				logPrintS(0,"[INF] A01. process stop by admin");
				continue;
			case 5: // info
				//SM7. info modify
				memset(gSenderInfo.szSmsTelcoInfo, 0x00, sizeof(gSenderInfo.szSmsTelcoInfo)); 
				memset(gSenderInfo.szSmsFlag, 0x00, sizeof(gSenderInfo.szSmsFlag)); 
				memset(gSenderInfo.szResMethod, 0x00, sizeof(gSenderInfo.szResMethod));
				memset(gSenderInfo.szTimeout, 0x00, sizeof(gSenderInfo.szTimeout));
				memset(gSenderInfo.szQType, 0x00, sizeof(gSenderInfo.szQType));
				memset(gSenderInfo.szBlockYN, 0x00, sizeof(gSenderInfo.szBlockYN));
				util.findValueParse(logonDbInfo.szReserve, "mms_tel", gSenderInfo.szSmsTelcoInfo); 
				util.findValueParse(logonDbInfo.szReserve, "mms_yn", gSenderInfo.szSmsFlag);
				util.findValueParse(logonDbInfo.szReserve, "res_method", gSenderInfo.szResMethod);
				util.findValueParse(logonDbInfo.szReserve, "timeout", gSenderInfo.szTimeout);
				util.findValueParse(logonDbInfo.szReserve, "q_type", gSenderInfo.szQType);
				util.findValueParse(logonDbInfo.szReserve, "block_yn",  gSenderInfo.szBlockYN);
				nCurAccCnt = 0;
				logPrintS(0,"[INF] gSenderInfo.szResMethod[%s]",gSenderInfo.szResMethod);
				logPrintS(0,"[INF] A02. info modify by admin [%s]", logonDbInfo.szReserve);
				break;

			default:
				break;
		}

		time(&SThisT);
		
		//SM10. 시간체크
		ret = (int)difftime(SThisT,SLastTLink);
		//if( ret > gConf.socketLinkTimeOut )
		if( 0 ) //사용x
		{
			logPrintS(0,"[ERR] socket link timeout - currTime/confTime [%d/%d]sec", ret, gConf.socketLinkTimeOut);
			bSActive = false;
			
			continue;
		}

		ret = (int)difftime(SThisT,monLastT);
		if( ret > 30 )
		{
			monitor.setDataSum(sum);
			monitor.setCurDate();
			monitor.send(gConf.monitorName);
			time(&monLastT);
			sum=0;
		}
				
		// 시간측정로그 : 메시지 수신전 시간
	 	struct tm *d;
		struct timeval val;
		/*				
		gettimeofday(&val, NULL);
		d=localtime(&val.tv_sec);		
		logPrintS(1,"0[Msg Recv Before]%04d%02d%02d,%02d:%02d:%02d-%06ld",d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
		*/				
		
		//SM11. socket read msg  3초 간격으로 변경확인 후 메세지 recv후 메세지 길이 리턴
		ret = hRemoteSock.recvAllMsg(3);
		//ret = util.recvPacket(hRemoteSock,buff,0,10000);
		if( ret < 0)
		{
			logPrintS(0,"[ERR] socket read msg failed - [%s]",hRemoteSock.getErrMsg());
			goto SenderEND;
		}
		      
		if( ret == 0 )
		{
			continue; // no data
		}
		
		// 시간측정로그 : 메시지 수신후 시간
//		gettimeofday(&val, NULL);
//		d=localtime(&val.tv_sec);		
		// logPrintS(1,"1[Msg Recv after]%04d%02d%02d,%02d:%02d:%02d-%06ld",d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
		// 시간측정로그 : 수신 데이터
//		gettimeofday(&val, NULL);
//		d=localtime(&val.tv_sec);		
		// logPrintS(1,"2[Data Recv Time]%04d%02d%02d,%02d:%02d:%02d-%06ld",d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
		// logPrintS(1,"3[Recv Data]\n%s",(char*)hRemoteSock.getMsg()); 오류 발생함
 
 
		// write log
		//로그 주석 처리.2011.11.23.
#ifdef _DEBUG
	    _monPrint(_MONILOG,(char*)hRemoteSock.getMsg());
#endif
			
#ifdef TIME
		/* gettimeofday */
		struct timeval timefirst, timesecond;
		struct timezone tzp;
		int secBuf, microsecBuf;
		float timeBuf;
		
		gettimeofday(&timefirst,&tzp);
		/* gettimeofday */
#endif
		//logPrintS(1,"nCurAccCnt(%d)",nCurAccCnt);
		//P16. DB접속 분류
		ret = classifyS(monitor, processInfo, logonDbInfo, db, hRemoteSock);
		
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] classifyS Error ret [%d]",ret);
			goto SenderEND;
		}
			
#ifdef TIME
		gettimeofday(&timesecond,&tzp);
		secBuf 		= (timesecond.tv_sec - timefirst.tv_sec);
		microsecBuf = (timesecond.tv_usec - timefirst.tv_usec);
		timeBuf 	= microsecBuf;
		timeBuf 	= timeBuf / 1000000;
		timeBuf 	= timeBuf + secBuf;
		logPrintS(0,"senderProcess db time [%f]",timeBuf);
#endif
    }

SenderEND:
	logPrintS(0,"[INF] socket END sockfd[%d]CID[%s]", hRemoteSock.getSockfd(), logonDbInfo.szCID);
	hRemoteSock.close();
	
	return;
}


int SenderProcess::classifyS(CMonitor& monitor,CProcessInfo& processInfo,CLogonDbInfo& logonDbInfo,CKSSocket& db,CKSSocket& hRemoteSock) 
{
	int ret = 0;
	int rptRet = 0;
	//char szYYYYMM[32];
	string strPacketHeader;
	
	CSenderDbMMSID senderDbMMSID;
	CBcastData bCastData;

	strPacketHeader = "";
	strPacketHeader.reserve(0);
			
	// 시간측정로그 : classifyS strPacketHeader.insert 시작
	struct tm *d;
	struct timeval val;
	// gettimeofday(&val, NULL);
	// d=localtime(&val.tv_sec);		
	//logPrintS(1,"4[classifyS strPacketHeader.insert Start]%04d%02d%02d,%02d:%02d:%02d-%06ld",d->tm_year + 1900, d->tm_mon+1, d->tm_mday,d->tm_hour, d->tm_min, d->tm_sec, val.tv_usec);
  
    //CF1. SOCKET MESSAGE INSERT TO PACKET
	strPacketHeader.insert(0,hRemoteSock.getMsg(),30);
  
	if( strstr(strPacketHeader.c_str(),"BEGIN PING\r\n") ) 
	{
		fflush(stdout);
		//logPrintS(0,"[INF] socket link recv");
		//CF2. sendPong
		ret = sendPong(hRemoteSock);
		monitor.setLinkTime();
		if( ret < 0 ) 
		{
			return ret;
		}
	} 
	else if ( strstr(strPacketHeader.c_str(),"BEGIN MMSSEND\r\n") )  
	{

		// 20140212 : mmsPacketSend.parse fix return 100
		/*
			return parse result
				-1 : MData Header info error
				 0 : OK
		*/
		
		//cout<<(char*)hRemoteSock.getMsg()<<endl;
		
		//CF3. packet parse
		ret = mmsPacketSend.parse((char*)hRemoteSock.getMsg());
		if( ret != 100 ) 
		{
			logPrintS(0,"[ERR] packet parse failed - ErrMsg[%s]",mmsPacketSend.getErrorMsg());
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"Msg Parsing Error");
			return ret;
		}

		//getMMSID2DB(db,senderDbMMSID,szSenderID);//디비 처리 하기
		/**====================================================================================**/
		//20140212 : ADD if 처리
		/**====================================================================================**/
		//CF4. db select get MMSID
		ret = getMMSID2DB(db,senderDbMMSID,szSenderID);
		//ret = getMMSID2DB(senderDbMMSID,szSenderID);
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] db select get MMSID failed cid[%s] ptn_sn[%s]",szSenderID,mmsPacketSend.getKeyValue());
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB GET MMS ID FAILED.");
			
			rptRet = setMMSRPTTBL(db, 0, 0, mmsPacketSend, 8003,"GetMMSIDFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] getMMSID2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s]",szSenderID,mmsPacketSend.getKeyValue());
			}
			
			return rptRet; 
		}
		//getCTNID2DB(db,senderDbMMSID,szSenderID);// 디비 처리 하기
		/**====================================================================================**/
		//20140212 : ADD if 처리
		/**====================================================================================**/
        //CF5. db select get CTNID 수정대상
		//ret = getCTNID2DB(db,senderDbMMSID,szSenderID);
		ret = getCTNID2DB(senderDbMMSID);
		if( ret < 0 )
		{
			logPrintS(0,"[ERR] db select get CTNID failed");
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB GET CTN ID FAILED.");
			return ret;
		}	
		/**====================================================================================**/

		writeLogMMSData(mmsPacketSend,senderDbMMSID.mmsid,senderDbMMSID.ctnid);// LOG write

		
		//CMMSFileProcess mmsFileProcess; write file object 사용X
 	
		//memset(szYYYYMM	,0x00	,sizeof(szYYYYMM));  
		//get_timestring("%04d%02d",time(NULL),szYYYYMM);
		//trim(szYYYYMM,strlen(szYYYYMM));


		//CF6. file write 사용X
		/**====================================================================================**/
		//20140212 ADD if
		/**====================================================================================**/
		//logPrintS(0,"[ERR] CF6. file write 사용X");

		/**====================================================================================**/
	

		//CF7. setMMSCTNTBL2DB(db,mmsFileProcess); insert CTL 사용X
		/**====================================================================================**/
		//20140212 ADD if
		/**====================================================================================**/
		//logPrintS(0,"[ERR] CF7. setMMSCTNTBL2DB 사용X");
		
		//CF8. setMMSTBL2DB 수정 대상
		/**====================================================================================**/
		//20140212 ADD if
		/**====================================================================================**/
	  	if(strncmp(gSenderInfo.szBlockYN,"Y",1)==0)
    	{
    		ret =  sendAck(hRemoteSock,mmsPacketSend,8012, senderDbMMSID.ctnid ,"Fail");
		
			if ( ret  < 0 )
			{
				logPrintS(0,"[ERR] socket send ack failed");
			}
			rptRet = setMMSRPTTBL(db, 0, senderDbMMSID.mmsid, mmsPacketSend, 8012, "SendBlocked.", szSenderID);
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] setMMSRPTTBL block cid[%s] ptn_sn[%s] mmsid[%lld]", 
					szSenderID, 
					mmsPacketSend.getKeyValue(), 
					senderDbMMSID.mmsid
					);
			}
			return 0;
		}
		else
		{
			ret = setMMSTBL2DB(db,senderDbMMSID.mmsid, senderDbMMSID.ctnid,logonDbInfo.nmPRT);  // send table
			
			if ( ret < 0 )
			{
				logPrintS(0,"[ERR] db insert table MMSTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid ,"DB INSERT MMSTBL FAILED.");
				
				rptRet = setMMSRPTTBL(db, 0,senderDbMMSID.mmsid,mmsPacketSend,8004,"InsertMMSTBLFailed.",szSenderID);
				
				if( rptRet < 0 )
				{
					logPrintS(0,"[ERR] setMMSTBL2DB setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
				}
				
				return rptRet;
			}
		}
		/**====================================================================================**/
		/*
			send queue
			1. 원본.2012.10.09 setMMSMSG2DB(db,senderDbMMSID.mmsid, senderDbMMSID.ctnid, mmsFileProcess,logonDbInfo.nmPRT);
			2. CID 값 추가.2012.10.09
		*/
		
		//CF9. setMMSMSG2DB을 setMMSMSG2DB_TALK로 변경
		/**====================================================================================**/
		//20140212 ADD if 
		/**====================================================================================**/
		//ret = setMMSMSG2DB(db, senderDbMMSID.szCid, senderDbMMSID.mmsid, senderDbMMSID.ctnid, mmsFileProcess,logonDbInfo.nmPRT);
		//ret = setMMSMSG2DB_TALK(db, senderDbMMSID.mmsid, mmsPacketSend, logonDbInfo.nmPRT);
		ret = setMMSMSG2DB_ATK_V3(db, senderDbMMSID.mmsid, mmsPacketSend, logonDbInfo.nmPRT);
		
		if ( ret < 0 )
		{
			logPrintS(0,"[ERR] db insert MMS MSG failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
			sendAck(hRemoteSock,mmsPacketSend,ret,senderDbMMSID.ctnid, "DB INSERT MMS MSG FAILED.");
			
			rptRet = setMMSRPTTBL(db,1,senderDbMMSID.mmsid,mmsPacketSend,8005,"InsertMMSMSGFailed.",szSenderID);
			
			if( rptRet < 0 )
			{
				logPrintS(0,"[ERR] setMMSMSG2DB_ATK_V3 setMMSRPTTBL failed cid[%s] ptn_sn[%s] mmsid[%lld]",szSenderID,mmsPacketSend.getKeyValue(),senderDbMMSID.mmsid);
			}
			
			return rptRet;
		}
		
		//CF10. 회신 번호 검사 사용X
		//logPrintS(0,"[INF] CF10. 회신 번호 검사 사용X");
		
		/**====================================================================================**/
		/*
			SendAck 부분을 밑에서 위로 옮김.2012.08.20
			동보 전송일 경우 레포트가 먼저 수신되는 경우가 생겨 옮김
		*/
		/**====================================================================================**/

		ret = 100;
		ret =  sendAck(hRemoteSock,mmsPacketSend,ret, senderDbMMSID.ctnid ,"Succ");
		if ( ret  < 0 )
		{
			logPrintS(0,"[ERR] socket send ack failed");
		}
		
		monitor.setDataTime();    
		nCurAccCnt++;
        
        //CF11. 발송제한 사용X
		//logPrintS(0,"[INF] CF11. 발송제한 사용X");
		
		/*
		 * CF11 : 전송건수 제한 체크
		 */
		ret =	SenderLimit(logonDbInfo);
		
		if(ret == -1)
		{
			logPrintS(0,"[ERR] limit SendreLimit ret [%d]",ret);
			return -1;
		}		//	logPrintS(1,"ret(%d)nCurAccCnt(%d)",ret,nCurAccCnt);
	} 
	else  // error
	{
		logPrintS(0,"[INF] invalid msg header data - [%s] ", strPacketHeader.c_str());

		fflush(stdout);
		ret = -1;
	}

	return ret;
}								

//사용X
int SenderProcess::SenderLimit(CLogonDbInfo& logonDbInfo)
{
	int ret;
	char szTemp[256];
	
	//* < brief 발송 제한 체크
	if (atoi(logonDbInfo.szLimitType) != 0)
	{
		memset(szTemp	,0x00	,sizeof(szTemp));
	
		get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), szLimitCurTime);	// 현재 날짜 구하기
		
		if (strcmp(szLimitTime,"") == 0)
		{
			strcpy(szLimitTime,szLimitCurTime);	// 년월일 값 구하기
		}

		ret = LimitCheck(logonDbInfo);
		//카운트 계산에 따른 서비스 제한 및 알림 기능 수행
		// logPrintS(1,"ret(%d)",ret);
		switch (ret)
		{
			case 9 : // 일 변경에 따른 누적 카운트 초기화 및 프로시저 실행
			case 10 : // 월 변경에 따른 누적 카운트 초기화 및 프로시저 실행
				if (ret == 9)
					logPrintS(1,"[INF]day change total count reset and run");
				if (ret == 10)
					logPrintS(1,"[INF]month change total count reset and run");
	
				//발송 제한 변수 초기화
				bDayWarnCheck = false;
				bMonWarnCheck = false;
				
				if (ret == 9)
					logonDbInfo.nMonAccCnt += logonDbInfo.nCurAccCnt;	//일 변경 시 월 카운트는 누적
				if (ret == 10)
					logonDbInfo.nMonAccCnt = 0;	//월 변경시 월 카운트는 초기화
					
				logonDbInfo.nDayAccCnt = 0;	//일,월 변경시 일 카운트는 항상 초기화
				nCurAccCnt = 0;	//일,월 변경시 현재 누적 카운트는 항상 초기화
				memset(szLimitTime,(char)NULL,sizeof(char)*16);
				break;
			default :
				break;
		}
				
		switch (ret)
		{
			case 0 : // 변경 없음
				break;
			case 1 : // 일 서비스 제한
				logPrintS(1,"[INF] daily limit [%d]"	,logonDbInfo.nDayLimitCnt);
				return -1;
			case 2 : // 월 서비스 제한
				logPrintS(1,"[INF] monthly limit [%d]"	,logonDbInfo.nMonLimitCnt);
				return -1;
			case 3 : // 일 서비스 제한	+ 알람
				sprintf(szTemp,"[ERR] daily limit - CID[%s] cnt[%d] Process close.", logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
				logPrintS(0,"[%s]"	,szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin daily service limit send failed",0,0);
				}
					
				return -1;
			case 4 : // 월 서비스 제한	+ 알람
				sprintf(szTemp,"[ERR] monthly limit - CID[%s] cnt[%d] Process close.", logonDbInfo.szCID, logonDbInfo.nMonLimitCnt);
				logPrintS(0,"[%s]",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin monthly service limit send failed",0,0);
				}
					
				return -1;
			case 5 : // 일 알람
				sprintf(szTemp,"[INF] daily limit orver - CID[%s] cnt[%d] alert msg send.", logonDbInfo.szCID, logonDbInfo.nDayLimitCnt);
				logPrintS(1,"[%s]",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin daily service limit send failed",0,0);
				}
					
				break;
			case 6 : // 월 알람
				sprintf(szTemp,"[INF] monthly limit over - CID[%s] cnt[%d] alert msg send.", logonDbInfo.szCID, logonDbInfo.nMonLimitCnt);
				logPrintS(1,"%s",szTemp);
				
				if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
				{
					monitoring("[ERR] alert2admin monthly service limit send failed",0,0);
				}
					
				break;
			case 7 : // 일 임계치 알람
				if (!bDayWarnCheck)
				{
					bDayWarnCheck = true;
					sprintf(szTemp,"[INF] daily limit warnning alert - CID[%s] cnt[%d]", logonDbInfo.szCID, logonDbInfo.nDayWarnCnt);
					logPrintS(1,"[%s]",szTemp);
					
					if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
					{
						monitoring("[ERR] alert2admin daily limit msg send failed",0,0);
					}
						
				}
				break;
			case 8 : // 월 임계치 알람
				if (!bMonWarnCheck)
				{
					bMonWarnCheck = true;
					sprintf(szTemp,"[INF] monthly limit warnning alert - CID[%s] cnt[%d]", logonDbInfo.szCID, logonDbInfo.nMonWarnCnt);
					logPrintS(1,"[%s]",szTemp);
					
					if ( Alert2Admin("msgbody=%s&type=%d", szTemp, 0) < 0 )
					{
						monitoring("[ERR] alert2admin monthly limit msg send failed",0,0);
					}
						
				}
				break;
			default :
				break;
		}
	}
	
	return 0;
}


//* < brief 발송 제한 체크
int SenderProcess::LimitCheck(CLogonDbInfo& logonDbInfo)
{
/* return value
	1 : 일 서비스 제한
	2 : 월 서비스 제한
	3 : 일 서비스 제한	+ 알람
	4 : 월 서비스 제한	+ 알람
	5 : 일 알람
	6 : 월 알람
	7 : 일 임계치 알람
	8 : 월 임계치 알람
	9 : 일 변경에 따른 누적 카운트 초기화
	10 : 월 변경에 따른 누적 카운트 초기화
	0 : 변경 없음
*/
	bool bDay=false;
	bool bMon=false;
	int  nDayAccCnt 		= logonDbInfo.nDayAccCnt;
	int  nMonAccCnt 		= logonDbInfo.nMonAccCnt;
	int  nDayWarnCnt 		= logonDbInfo.nDayWarnCnt;
	int  nMonWarnCnt 		= logonDbInfo.nMonWarnCnt;
	int  nDayLimitCnt 		= logonDbInfo.nDayLimitCnt;
	int  nMonLimitCnt 		= logonDbInfo.nMonLimitCnt;
	int  nLimitType 		= atoi(logonDbInfo.szLimitType);
	int  nLimitFlag 		= atoi(logonDbInfo.szLimitFlag);

	//logPrintS(1,"szLimitCurTime(%s)szLimitTime(%s)",szLimitCurTime,szLimitTime);
	if (strncmp(szLimitTime	,szLimitCurTime	,8) != 0)
		bDay = true;	//일 단위
		
	if (strncmp(szLimitTime,szLimitCurTime,6) != 0) 
		bMon = true;	//월 단위

	if (bDay)
	{
		if (bMon)
		{
			return 10;	// 월 변경이 이루어 졌을 경우
		}
		else
		{
			return 9;	// 일 변경이 이루어 졌을 경우
		}
	}
 //logPrintS(1,"nLimitType(%d),nDayWarnCnt(%d),nCurAccCnt(%d),nDayAccCnt(%d)",nLimitType, nDayWarnCnt, nCurAccCnt,nDayAccCnt);
	//서비스 제한 체크
	switch (nLimitType)
	{
		case 0:	//발송 제한 적용 안함
			return 0;
		case 1:	//일,월 발송 제한
		case 2:	//일 발송 제한
		case 3:	// 월 발송 제한
			if (nLimitType == 1 || nLimitType == 2)
			{
				//일 임계치 체크 (임계치와 제한 건수 사이)
				if (((nDayAccCnt + nCurAccCnt) > nDayWarnCnt) && ((nDayAccCnt + nCurAccCnt) < nDayLimitCnt))
				{
					logPrintS(1,"[INF] daily limit - limit over [%d/%d]", nDayWarnCnt, (nDayAccCnt + nCurAccCnt)-nDayWarnCnt);
					return 7;
				}
				//일 서비스 제한 체크
				if ((nDayAccCnt + nCurAccCnt) > nDayLimitCnt)
				{
					logPrintS(1,"[INF] daily limit - config value [%d]", nDayLimitCnt);
				
					switch (nLimitFlag)
					{
						case 1 :
							return 1;
						case 2 :
							return 3;
						case 3 :
							return 5;
						default :
							return 0;
					}
				}
			}
//logPrintS(1,"nMonWarnCnt(%d),nMonAccCnt(%d),nMonLimitCnt(%d)",nMonWarnCnt, nMonAccCnt+nCurAccCnt+nDayAccCnt, nMonLimitCnt);

			if (nLimitType == 1 || nLimitType == 3)
			{
				//월 임계치 체크 (임계치와 제한 건수 사이)
				//20180829 월 임계치 체크 변경 nDayAccCnt 추가
				//if (((nMonAccCnt + nCurAccCnt) > nMonWarnCnt) && ((nMonAccCnt + nCurAccCnt) < nMonLimitCnt))
				if (((nMonAccCnt + nCurAccCnt + nDayAccCnt) > nMonWarnCnt) && ((nMonAccCnt + nCurAccCnt + nDayAccCnt) < nMonLimitCnt))
				{
					logPrintS(1,"[INF] monthly limit - limit over [%d/%d]", nMonWarnCnt, (nMonAccCnt + nCurAccCnt + nDayAccCnt)-nMonWarnCnt);
					return 8;
				}
				//월 서비스 제한 체크
				//	if ((nMonAccCnt + nCurAccCnt) > nMonLimitCnt)
				if ((nMonAccCnt + nCurAccCnt + nDayAccCnt) > nMonLimitCnt)
				{
					logPrintS(1,"[INF] monthly limit - config value [%d]", nMonLimitCnt);
					switch (nLimitFlag)
					{
						case 1 :
							return 2;
						case 2 :
							return 4;
						case 3 :
							return 6;
						default :
							return 0;
					}
				}
			}
		default:
			return 0;
		break;
	}
	return 0;
}

int setMMSRPTTBL(CKSSocket& db,int type, long long nMMSId, CMMSPacketSend& mmsPacketSend, int nResCode,char* res_text,char* cid)
{
	int ret;
	CSenderDbMMSRPTQUE senderDbMMSRPTQUE;
	/*CMData mData;
	ret = mmsPacketSend.getMDataFirst(mData);
	if( ret != 0 )
	{
		logPrintS(1,"[%s] mmsPacketSend.getMDataFirst Err", __func__);
		return -1;
	}*/

	memset(&senderDbMMSRPTQUE,0x00,sizeof(senderDbMMSRPTQUE));
	
	senderDbMMSRPTQUE.header.type = SETSENDRPT;
	senderDbMMSRPTQUE.header.leng = sizeof(CSenderDbMMSRPTQUE) - sizeof(Header);

	/*if(strncmp(mData.strEncoding.c_str(), " aes_base64", 10) == 0 || strncmp(mData.strEncoding.c_str(), "aes_base64", 10) == 0)
	{
		int size;
		unsigned char *receiverNum = (unsigned char*)__base64_decode((unsigned char *)mmsPacketSend.getReceiverValue(), strlen(mmsPacketSend.getReceiverValue()), &size);
		Encrypt en;
		en.set_key();
		//logPrintS(1,"receiverNum:%d", atoi(mmsPacketSend.getPhoneSizeValue()));
		en.decrypt(receiverNum, receiverNum, atoi(mmsPacketSend.getPhoneSizeValue()));
		strcpy(senderDbMMSRPTQUE.szDstAddr, (char*)receiverNum);
		free(receiverNum);
	}
	else
	{
		strcpy(senderDbMMSRPTQUE.szDstAddr , mmsPacketSend.getReceiverValue());
	}*/
	
	strcpy(senderDbMMSRPTQUE.szDstAddr , "010");
	
	strcpy(senderDbMMSRPTQUE.szPtnSn   , mmsPacketSend.getKeyValue()     );
	senderDbMMSRPTQUE.nMMSId 		= nMMSId;
	senderDbMMSRPTQUE.res_code     = nResCode;
	strcpy(senderDbMMSRPTQUE.res_text   , res_text);
	senderDbMMSRPTQUE.nTelcoId = getTelcoId(mmsPacketSend.getImgCnt(), gSenderInfo.szSmsTelcoInfo, gSenderInfo.szQType);
	senderDbMMSRPTQUE.nType 		= type;
	memcpy(senderDbMMSRPTQUE.szCid, cid, 10);
	
	
	CSenderDbInfoAck senderDbInfoAck;
	CDBUtil dbUtil;

	//SMT3. 데이타 senderMMSDB로 전송
	dbUtil.sendQuery(db, (void*)&senderDbMMSRPTQUE,(void*)&senderDbInfoAck,gConf.dbRequestTimeOut,senderDbDomainName,ret);
	
	
	/*
	if (ret < 0)
	{
		if(type == 1)
		{
			logPrintS(0,"[ERR] setSendReportData TBL MMSID[%lld] PtnSn[%s] ret[%d]",nMMSId, senderDbMMSRPTQUE.szPtnSn  , ret);
		}else
		{
			logPrintS(0,"[ERR] setSendReportData QUEUE MMSID[%lld] PtnSn[%s] ret[%d]",nMMSId, senderDbMMSRPTQUE.szPtnSn  , ret);	
		}
	}*/
	
	return ret;
}
