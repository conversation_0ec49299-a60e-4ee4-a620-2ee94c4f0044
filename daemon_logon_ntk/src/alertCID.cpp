#include "alertCommon.h"
#include "string.h"
#include <iostream>
#include <sqlca.h>

using namespace std;

/* Alert2Admin */
//#define CALL_WEB	"211.43.202.32"
#define CALL_WEB	"210.116.121.230"
#define PORT_WEB	80
#define WEB_PAGE	"/alertcall/alertcall.php"
#define	SVC_TYPE	 1
#define	CALL_TYPE	710
//#define SQLCODE "sqlca.sqlcode"

  EXEC SQL BEGIN DECLARE SECTION;
char *DB_SID="NEOMMS";
char *DB_ID="NEOMMS";
char *DB_PASS="NEOMMS";
  EXEC SQL END DECLARE SECTION;

char _DATALOG[64];
char _LOGBUF[2048];



int strReplace(char* sourceMsg , char* a , char* b)
{
    int cnt;
    char* pstrtmp;
    char msgtmp[512];
    pstrtmp = (char*)strstr(sourceMsg,a);
    while(pstrtmp)
    {
        memset(msgtmp,0x00,sizeof(msgtmp));
        memcpy(msgtmp,sourceMsg,(cnt = (int)(pstrtmp - sourceMsg)));
        memcpy(msgtmp+cnt,b,strlen(b));
        memcpy(msgtmp+cnt+strlen(b),pstrtmp+strlen(a),strlen(pstrtmp+strlen(a)));
        strcpy(sourceMsg,msgtmp);
        pstrtmp = (char*)strstr(pstrtmp+strlen(b),a);
    }
    return 1;
}


int Alert2Admin(char* pForm,...)
{
    int sockfd, len;
    struct sockaddr_in serv_addr;
    struct timeval tv;

    char strParam[256];
    char transBuf[350];

    va_list pArg;
    va_start(pArg, pForm);
    vsprintf(strParam, pForm, pArg);
    va_end(pArg);

    strReplace(strParam,(char*)" ",(char*)"%20");
    memset(transBuf,0x00,sizeof(transBuf));
    sprintf(transBuf,"get %s?%s\n",WEB_PAGE,strParam);
    printf("\nAlert2Admin:[%s]\n",transBuf);

//		sprintf(_LOGBUF,"[DEBUG] Alert2Admin transBuf[%s]", transBuf);
//		_logPrint(_DATALOG, _LOGBUF);
		
		
    memset((char*)&serv_addr,0x00,sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_addr.s_addr = inet_addr(CALL_WEB);
    serv_addr.sin_port = htons(PORT_WEB);

    if ( (sockfd=socket(AF_INET,SOCK_STREAM,0))<0 ) {
        return -1;
    }

    tv.tv_sec = 5;
    tv.tv_usec = 0;

    if ( setsockopt(sockfd,SOL_SOCKET,SO_SNDTIMEO,&tv,sizeof(tv)) < 0 ) {
        return -1;
    }

    if ( setsockopt(sockfd,SOL_SOCKET,SO_RCVTIMEO,&tv,sizeof(tv)) < 0 ) {
        return -1;
    }

    if ( connect(sockfd,(struct sockaddr*)&serv_addr, sizeof(struct sockaddr))<0 ) {
        return -1;
    }
    len = write(sockfd,transBuf,strlen(transBuf));
    if( len < 0 )
        return -1;
    memset(transBuf,0x00,sizeof(transBuf));
    len = read(sockfd,transBuf,sizeof(transBuf));
    close(sockfd);
    if (strncmp(transBuf,"OK",2) != 0 )
        return -1;
    printf("\n%s\n",transBuf);
    return 0;
}


char* trim(char* szOrg, int leng)
{
    int i = 0;
    for (i=leng-1;i>=0;i--) {
        if( (szOrg[i]==0x20)||(szOrg[i]==' ')||(szOrg[i]==0x00) ) {
            szOrg[i] = 0x00;
        }
        else break;
    }
    return szOrg;
}

void get_timestring2(char *s)
{
	struct timespec tmv;
	struct tm	tp;

	clock_gettime(CLOCK_REALTIME, &tmv);
	localtime_r(&tmv.tv_sec, &tp);
	sprintf(s, "%04d%02d%02d,%02d:%02d:%02d.%09d",
		tp.tm_year+1900, tp.tm_mon+1, tp.tm_mday,
		tp.tm_hour, tp.tm_min, tp.tm_sec,
		(int)tmv.tv_nsec
		);
    s[strlen(s)] = ' ';
}

int _logPrint(char* szPath, char* szLog)
{
    static int fd=0;
    int writeLen;

    static char file[256]="";
    char date[32];
    char buff[2048];
    static char current_date[16]="";

    memset(date,0x00,sizeof(date));
    memset(buff,0x00,sizeof(buff));
    get_timestring2(date);


    if( memcmp(date,current_date,8) == 0 ) {
        /* 이미 있는 파일 */
        sprintf(buff,"[%s]:%s\n",date,szLog);

        writeLen = write(fd,buff,strlen(buff));
        if( writeLen <= 0 )
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            sprintf(szTmp,"LogWrite file errror !![%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            close(fd);
            exit(-5);
        }



    } else {
        /* 새로 만들어야 하는 파일 */
        if( fd ) close(fd);
        strncpy(current_date, date, 8);
        current_date[8] = '\0';

/*        strcpy(file, LOGFILEDIR);
 */
        strcpy(file, szPath);
        strcat(file, ".");
        strcat(file, current_date);

        fd = open( file, CREATE_FLAGS, CREATE_PERMS );
        if( fd < 0 )
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            sprintf(szTmp,"log File Open fail : [%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            close(fd);
            exit(-5);
        }

        sprintf(buff,"[%s]:%s\n",date,szLog);

        writeLen = write(fd,buff,strlen(buff));
        if( writeLen <= 0 )
        {
            char szTmp[SOCKET_BUFF];
            memset(szTmp,0x00,sizeof szTmp);
            sprintf(szTmp,"LogWrite file errror !![%s][%s]\n[%s]",
                    strerror(errno),file,szLog);
            printf(szTmp);
            syslog(LOG_ALERT,szTmp,strlen(szTmp));
            close(fd);
            exit(-5);
        }


    }

    return 0;
}

int check_process(char *proc_name)
{

 char	temp[256], temp_file[256];
 FILE*	temp_fd;
 int ret = -1;


    strcpy(temp_file, "/data/log_alert/ps_tmp_file");
    unlink(temp_file);

	//sprintf(temp, "ps -ef | grep %s  | grep kko_atk | grep -v grep > ", proc_name);
	sprintf(temp, "ps -ef | grep %s  | grep -v grep > ", proc_name);
    strcat(temp, temp_file);
    system(temp);

    if ((temp_fd = fopen(temp_file, "r")) == NULL)
    {
        sprintf(_LOGBUF, "in check_process : %s fopen error.\n", temp_file);
		_logPrint(_DATALOG, _LOGBUF);
		fclose(temp_fd);
        return ret;
    }

    while (fgets(temp, sizeof(temp), temp_fd) != NULL)
    {
        if (strstr( temp, proc_name ) !=  NULL )
        {
            ret = 1;
            break;
        }
    }

    fclose(temp_fd);
    unlink(temp_file);


//		sprintf(_LOGBUF,"[DEBUG] check_process ret[%d]", ret);
//		_logPrint(_DATALOG, _LOGBUF);
		
	return ret;

}


void combine_dst(char *dst, char *addr1, char *addr2, char *addr3 )
{

	if( strlen(addr1) > 0 )
		strcpy(dst, addr1 );

	if( strlen(addr2) > 0 )
	{
		strcat(dst, "|");
		strcat(dst, addr2 );
	}
	if( strlen(addr3) > 0 )
	{
		strcat(dst, "|");
		strcat(dst, addr3 );
	}

	return;
}

int loadCID() 
{

	char proc_name[64]; 
	char dst_addr[64];
	
  EXEC SQL BEGIN DECLARE SECTION;
  	char cid[40+1];
  	char cpname[50+1];
	char msg_body[150+1];
	char dst_addr1[16+1];
	char dst_addr2[16+1];
	char dst_addr3[16+1];
  EXEC SQL END DECLARE SECTION;
	
	EXEC SQL DECLARE cur_alert_cid CURSOR FOR SELECT CID, CPNAME, MSG_BODY, DST_ADDR1, DST_ADDR2, DST_ADDR3 FROM TBL_ALERT_CID WHERE USE_YN = 'Y';
		
	EXEC SQL OPEN cur_alert_cid;
	EXEC SQL WHENEVER NOT FOUND DO break;
	
	while(1)
	{

		memset(cid, 0x00, sizeof(cid));
		memset(cpname, 0x00, sizeof(cpname));
		memset(msg_body, 0x00, sizeof(msg_body));
		memset(dst_addr1, 0x00, sizeof(dst_addr1));
		memset(dst_addr2, 0x00, sizeof(dst_addr2));
		memset(dst_addr3, 0x00, sizeof(dst_addr3));
	
		EXEC SQL FETCH cur_alert_cid INTO :cid, :cpname, :msg_body, :dst_addr1, :dst_addr2, :dst_addr3;

		if( sqlca.sqlcode != 0 && sqlca.sqlcode != -1405 )
		{
			EXEC SQL CLOSE cur_alert_cid;
			EXEC SQL COMMIT WORK RELEASE;
			
			sprintf(_LOGBUF,"[ERR] FETCH Fail CID[%s] code[%d]", cid, sqlca.sqlcode);
			_logPrint(_DATALOG, _LOGBUF);
			return -1;
		}

		trim(cid, strlen(cid));
		trim(cpname, strlen(cpname));
		trim(msg_body, strlen(msg_body));
		trim(dst_addr1, strlen(dst_addr1));
		trim(dst_addr2, strlen(dst_addr2));
		trim(dst_addr3, strlen(dst_addr3));


		sprintf(proc_name, "ID_S_%s", cid);
		
		sprintf(_LOGBUF,"[DEBUG] proc_name[%s]", proc_name);
		_logPrint(_DATALOG, _LOGBUF);


		if( check_process ( proc_name ) < 0 )
		{
			combine_dst( dst_addr, dst_addr1, dst_addr2, dst_addr3 );

			sprintf(_LOGBUF,"[DEBUG] dst_addr[%s]", dst_addr);
			_logPrint(_DATALOG, _LOGBUF);

			if ( Alert2Admin("msgbody=%s %s&type=%d&callType=%d&phoneNum=%s&cpName=%s", proc_name, msg_body, SVC_TYPE, CALL_TYPE, dst_addr, cpname ) < 0 )
			{
				sprintf(_LOGBUF,"[ERR] alert2admin daily service limit send failed cid[%s] proc[%s] dst_addr[%s]", cid, proc_name, dst_addr);
				_logPrint(_DATALOG, _LOGBUF);

			}
		}

		sprintf(proc_name, "ID_R_%s", cid);

		sprintf(_LOGBUF,"[DEBUG] proc_name[%s]", proc_name);
		_logPrint(_DATALOG, _LOGBUF);
		
		if( check_process ( proc_name ) < 0 )
		{
			combine_dst( dst_addr, dst_addr1, dst_addr2, dst_addr3 );
			
			sprintf(_LOGBUF,"[DEBUG] dst_addr[%s]", dst_addr);
			_logPrint(_DATALOG, _LOGBUF);
			
			if ( Alert2Admin("msgbody=%s %s&type=%d&callType=%d&phoneNum=%s&cpName=%s", proc_name, msg_body, SVC_TYPE, CALL_TYPE, dst_addr, cpname ) < 0 )
			{
				sprintf(_LOGBUF,"[ERR] alert2admin daily service limit send failed cid[%s] proc[%s] dst_addr[%s]", cid, proc_name, dst_addr);
				_logPrint(_DATALOG, _LOGBUF);

			}
		}

	} // while
	
	EXEC SQL CLOSE cur_alert_cid;
	EXEC SQL COMMIT WORK RELEASE;
	

    return 0;
}


int main(int argc, char* argv[])
{
    int ret;

	sprintf(_DATALOG, "/data/log_alert/ALERT_CID_LOG");
    
    sprintf(_LOGBUF,"alertCID Process START!!! " );
		_logPrint(_DATALOG, _LOGBUF);

    EXEC SQL CONNECT :DB_ID IDENTIFIED BY :DB_PASS USING :DB_SID;
    if(sqlca.sqlcode != 0 )
    {
        sprintf(_LOGBUF,"[ERR] db connect failed[%d]\n- SID[%s]\n- ID[%s]\n- PW[%s]", sqlca.sqlcode, DB_SID, DB_ID, DB_PASS );
		_logPrint(_DATALOG, _LOGBUF);
        goto END;
    }


	loadCID();

  sprintf(_LOGBUF,"alertCID Process END!!! " );
	_logPrint(_DATALOG, _LOGBUF);

	return 0;



END:
	sprintf(_LOGBUF,"alertCID Process END!!! " );
	_logPrint(_DATALOG, _LOGBUF);

    return 0;
}
