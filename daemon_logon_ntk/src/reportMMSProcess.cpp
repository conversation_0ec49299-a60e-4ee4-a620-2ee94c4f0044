#include "reportMMSProcess.h"
#include <string>
#include "mmsPacketBase.h"


CMMSPacketBase mmsPacketBase;
using namespace std;

int main(int argc,char* argv[])
{
	/*
	 * 1 : sockfd
	 * 2 : pipe
	 * 3 : version 
	 * 4 : conf file
	 */
	
	int sockfd;
	int fd;
	int ret;
	char* p;
	char buff[SOCKET_BUFF];
	CLogonDbInfo logonDbInfo;
	
	sockfd = atoi(argv[1]);
	fd = atoi(argv[2]);
	
	memset(&logonDbInfo,0x00,sizeof(logonDbInfo));
	read(fd,(char*)&logonDbInfo,sizeof(logonDbInfo));
	close(fd);
	
	memset(_DATALOG,0x00,sizeof(_DATALOG));//CCL(_DATALOG);
	memset(_MONILOG,0x00,sizeof(_MONILOG));//CCL(_MONILOG);
	
	sprintf(_DATALOG,"%s/",logonDbInfo.szLogPath);
	sprintf(_MONILOG,"%s/",logonDbInfo.szLogPath);
	
	memset(szReportID,0x00,sizeof(szReportID));//CCL(szReportID);
	strcpy(szReportID,logonDbInfo.szCID);
	
	p = strtok(logonDbInfo.szLogFilePath,"|");
	
	if( p )
	{
		strcat(_MONILOG,p);
	}
	else
	{
		logPrintR(0,"[ERR] logondbinfo logfilepath failed - get monitor [%s]",logonDbInfo.szLogFilePath);
		return -1;
	}
	
	p = strtok(NULL,"|");
	if( p )
	{	
		strcat(_DATALOG,p);
	}    
	else
	{
		logPrintR(0,"[ERR] logondbinfo logfilepath failed - get data [%s]",logonDbInfo.szLogFilePath);
		return -1;
	}
	
	logPrintR(0,"[INF] filepath - logfile[%s]monitorfile[%s]", _DATALOG, _MONILOG);
	
	char szReportDBIP[16];
	char szReportDBName[64];
	
	memset(szReportDBIP		,0x00	,sizeof(szReportDBIP));
	memset(szReportDBName	,0x00	,sizeof(szReportDBName));
	
	strcpy(szReportDBName,logonDbInfo.szReportDBName);
	
	p = strtok(szReportDBName,":");
	if( p )
	{
	    strcpy(szReportDBIP,p);
	}
	else 
	{
	    logPrintR(0,"[ERR] logonDbinfo reportDBName[%s]",logonDbInfo.szReportDBName);
	    return -1;
	}
	
	sprintf(reportDbDomainName,"%s/",logonDbInfo.szDomainPath);
	
	p = strtok(NULL,":");
	if( p )
	{	    
		strcat(reportDbDomainName,p);
	}
	else 
	{
		logPrintR(0,"[ERR] logondbinfo reportDBdomainName[%s]",logonDbInfo.szReportDBName);
		return -1;
	}
	
	logPrintR(0,"[INF] logondbinfo reportDbDomainName[%s]",reportDbDomainName);
	
	ret = configParse(argv[4]);
	if( ret != 0 )
	{
		exit(1);
	}
	
	logPrintR(0,"[INF] conf logonDBName - [%s]",gConf.logonDBName);
	
	reportProcess(sockfd,logonDbInfo);
	
	return 0;
}


void reportProcess(int sockfd,CLogonDbInfo& logonDbInfo)
{
	int ret;
	CLogonUtil util;
	CAdminUtil admin;
	CKSSocket db;
	char szAppName[16];
	CProcessInfo processInfo;
	CMonitor monitor;

	memset(&processInfo	,0x00	,sizeof(processInfo));
	memset(szAppName	,0x00	,sizeof(szAppName));	//CCL(szAppName);
	
	//sprintf(szAppName,"%d_%d",logonDbInfo.nmPID,logonDbInfo.nmJOB);
	strcpy(szAppName,logonDbInfo.szAPPName);

	strcpy(processInfo.processName,logonDbInfo.szReportName);
	get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), processInfo.startTime);
	sprintf(processInfo.szPid,"%d",getpid());
	strcpy(processInfo.logonDBName,gConf.logonDBName);
 
	logPrintR(0,"[INF] reportProcess start sockfd[%d]CID[%s]currentTime[%s]pid[%s]",sockfd,logonDbInfo.szCID,processInfo.startTime,processInfo.szPid);

	util.displayLogonDbInfo(logonDbInfo,_MONILOG);

	char buff[SOCKET_BUFF];
	int recvLen;
	CKSSocket hRemoteSock;	
	hRemoteSock.attach(sockfd);
    
	ret = admin.createDomainID(logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);

	if( ret != 0 )
	{
		logPrintR(0,"[ERR] socket_domain create failed CID[%s]classify[%c]conf.domainPath[%s]",logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);
		bRActive = false;
	}

	monitor.Init("logon7","report",processInfo.processName,logonDbInfo.szCID,logonDbInfo.nmPID,logonDbInfo.szIP);
	
	
	
	time(&RLastTLink);
	while(bRActive)
	{
		wait_a_moment(logonDbInfo.nRptWait); 
		ret = admin.checkPacket(processInfo,logonDbInfo,sum); // check admin packet
		if( ret < 0 )
		{
			logPrintR(0,"[ERR] socket_domain link check failed - CID[%s]ErrMsg[%s]",logonDbInfo.szCID,admin.getErrMsg());
			bRActive = false;
			continue;
		}

		switch(ret)
		{
			case 3: // end
				bRActive = false;
				continue;
			default:
				break;
		}
		
		time(&RThisT);
		ret = (int)difftime(RThisT,RLastTLink);
		//if( ret > gConf.socketLinkTime)
		//20131122 레포트 연결체크 주기 변경 3초에서 30초로
		
		if( ret > 3 )
		{
			ret = sendLink(hRemoteSock,util);
			if( ret < 0 )
			{
				logPrintR(0,"[ERR] socket link check failed - CID[%s]ErrMsg[%s]",logonDbInfo.szCID,admin.getErrMsg());
				bRActive = false;
				continue;
			}

			get_timestring("%04d%02d%02d%02d%02d%02d", time(NULL), processInfo.linkTime);
			monitor.setLinkTime();

		}

		ret = (int)difftime(RThisT,monLastT);
		if( ret > 30 )
		{
			monitor.setDataSum(sum);
			monitor.setCurDate();
			monitor.send(gConf.monitorName);
			time(&monLastT);
			sum=0;
		}

		memset(buff	,0x00	,sizeof(buff));//CCL(buff);
	
	
	
	
		ret = hRemoteSock.recvAllMsg();
		//ret = util.recvPacket(hRemoteSock,buff,0,10000);
		if( ret < 0)
		{
			logPrintR(0,hRemoteSock.getErrMsg());
			bRActive = false;
			continue;
		}





		if( ret > 0 )
		{
			ret = classifyR(hRemoteSock,buff);
			if( ret < 0 )
			{
				logPrintR(0,"[ERR] socket classifyR failed - CID[%s]ErrMsg[%s]",logonDbInfo.szCID,admin.getErrMsg());
				bRActive = false;
				continue;
			}
		}





		// get Report and send Rpt
		ret = getReprot(monitor,db,hRemoteSock,util,szAppName,logonDbInfo.nRptNoDataSleep);
		if( ret < 0 )
		{
			logPrintR(0,"[ERR] socket getReprot - CID[%s]ErrMsg[%s]",logonDbInfo.szCID,admin.getErrMsg());
			bRActive = false;
			continue;
		}

	}
	logPrintR(0,"[INF] socket end - sockfd[%d]CID[%s]",hRemoteSock.getSockfd(),logonDbInfo.szCID);
	
	hRemoteSock.close();
/*
	ret = admin.deleteDomainID(logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);
	if( ret != 0 )
	{
      logPrintR(0,"DOMAIN delete Error [%s][%c]path[%s]",logonDbInfo.szCID,logonDbInfo.classify,gConf.domainPath);
	}
*/
	return ;
}


int classifyR(CKSSocket& hRemoteSock,char* buff)
{
	int ret=0;
	int nType = 0;

	TypeHeader* pHeader = (TypeHeader*)buff;
	nType = str2int(pHeader->msgType,sizeof(pHeader->msgType));

	switch(nType) 
	{
		default:
			logPrintR(0,"[ERR] msg header check failed - msgType[%d]",nType);
			viewPackReport(buff,str2int(pHeader->msgLeng,sizeof(pHeader->msgLeng)));
			ret = -1;
			break;
	}

	return ret;
}


int sendLink(CKSSocket& hRemoteSock, CLogonUtil& util)
{
	int ret;
	string strPacket;

	strPacket = "BEGIN PING\r\nKEY:1234\r\nEND\r\n";

	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
//logPrintR(0,"REPORT PING [%d]\n",ret);

	if( ret != strPacket.length())
	{
		logPrintR(0,"[ERR] socket ping failed - errno[%s]sendSize[%d/%d]",strerror(errno),ret,strPacket.length());
		return -1;
	}

	//_monPrint(_MONILOG,(char*)strPacket.c_str());
	//printf("REPORT PING [%s]\n",strPacket.c_str());
	fflush(stdout);
	//logPrintR(0,"Link Send");

	ret = hRemoteSock.recvAllMsg(10);
//logPrintR(0,"REPORT PONG [%s]\n",hRemoteSock.getMsg());
	if( ret > 0 )
	{
		//printf("REPORT PONG [%s]\n",hRemoteSock.getMsg());
		fflush(stdout);
		//logPrintR(0,"Link Recv");
	} 
	else 
	{
		printf("REPORT LINK ERROR\n");
		fflush(stdout);
		logPrintR(0,"[ERR] socket pong failed - errno[%s]ret[%d]sockMsg[%s]",strerror(errno),ret,hRemoteSock.getMsg());
		return -1;
	}

	time(&RLastTLink);

	return 0;
}


void logPrintR(int type, const char *format, ...)
{
	va_list args;
	char logMsg[SOCKET_BUFF];
	char tmpMsg[SOCKET_BUFF];

	va_start(args, format);
	vsprintf(tmpMsg, format, args);
	va_end(args);

	sprintf(logMsg,"[R][%s] %s",szReportID,tmpMsg);

	if (type==1)
	{
		_logPrint(_DATALOG,logMsg);
	}
	else
	{
		_monPrint(_MONILOG,logMsg);
	}
	
	return ;
}


int getReprot(CMonitor& monitor,CKSSocket& db,CKSSocket& hRemoteSock,CLogonUtil& util,char* szAppName,int nRptNoDataSleep)
{


	static bool sleepReportFlag = false;
	static time_t LastT;
	
	int ret;
//	int nMsgId;
	long long nMsgId;
	char dbBuff[SOCKET_BUFF];
	
	CReportDbInfo rptData;

	if( sleepReportFlag )
	{
		time_t ThisT;
		time(&ThisT);

		if( difftime(ThisT,LastT) > nRptNoDataSleep )
		{
			sleepReportFlag = false;
		}

		return 0;
	}

	memset((char*)&rptData,0x00,sizeof(CReportDbInfo));

	strcpy(rptData.header.msgType,"1");
	strcpy(rptData.szResvData,szAppName);

	ret = db.connectDomain(reportDbDomainName);

	if( ret != 0 )
	{
		logPrintR(0,"[ERR] socket_domain connect failed - errno[%s]",strerror(errno));
		sleepReportFlag = true;
		time(&LastT);
		db.close();

		return 0;
	}
	ret = db.send((char*)&rptData,sizeof(CReportDbInfo));
	
	

	if( ret != sizeof(CReportDbInfo))
	{
		logPrintR(0,"[ERR] socket_domain send failed - send reportInfo size[%d/%d] errno[%s]",ret,sizeof(CReportDbInfo),strerror(errno));
		sleepReportFlag = true;
		time(&LastT);
		db.close();

		return 0;
	}

	ret = db.select(120,0);

	if( ret == 0 )
	{
		logPrintR(0,"[ERR] socket_domain recv timeout - time out [120]sec");
		sleepReportFlag = true;
		time(&LastT);
		db.close();

		return 0;
	}

	if( ret < 0 )
	{
		logPrintR(0,"[ERR] socket_domain select failed - errno[%s]",strerror(errno));
		sleepReportFlag = true;
		time(&LastT);
		db.close();

		return 0;
	}

	memset(dbBuff	,0x00	,sizeof(dbBuff)); //CCL(dbBuff);
	ret = db.rcvmsg(dbBuff);
	if( ret == 0 || ret < 0 )
	{
		logPrintR(0,"[ERR] socket_domain recv failed - errno[%s]ret[%d]",strerror(errno),ret);
		sleepReportFlag = true;
		time(&LastT);
		db.close();

		return 0;
	}
	db.close();

	memcpy((char*)&rptData,dbBuff,ret);




	//viewPackReport(dbBuff,ret);// LSY 20131115 테스트

	CReportDbInfo* pRpt = (CReportDbInfo*)dbBuff;
	
	//if( memcmp(pRpt->szResCode,"99",2) == 0 )
	if( memcmp(pRpt->szResCode,"99",strlen(pRpt->szResCode)) == 0 )
	{ /* no report */
		/* #define NOREPORTSLEEPTIME  */
		sleepReportFlag = true;
		time(&LastT);
		db.close();

		
		return 0;
	}

	string strPacket;
	string strKey;
	string strCode;
	string strTime;
	string strDesc;
	string strEndTelco;

	strKey 		= pRpt->szPtnsn;
	strCode 	= pRpt->szResCode;
	strTime 	= pRpt->szRptDate;
	strEndTelco = pRpt->szEndTelco;
	strDesc 	= "succ";

	strPacket = "BEGIN REPORT\r\nKEY:" + strKey + "\r\nCODE:" + strCode + "\r\nTIME:"
				+ strTime + "\r\nDESC:" + strDesc + "\r\nNET:" + strEndTelco + "\r\nEND\r\n";
   





	ret = hRemoteSock.send((char*)strPacket.c_str(),strPacket.length());
	
	if( ret != strPacket.length() )
	{
		logPrintR(0,"[ERR] socket send ack failed - send size[%d/%d]errno[%s]",ret,strPacket.length(),strerror(errno));
		/* report 재전송 */
		//retryReport(db,rptData);
		return -1;
	}

	fflush(stdout);

	nMsgId = pRpt->nMsgId;

    //20180829 key 20 -> 32
	logPrintR(1,"[INF] RPT msg mmsid[%lld] key[%.32s]rescode[%s]date[%.14s]telco[%.3s]"
            ,nMsgId
            ,pRpt->szPtnsn
            ,trim(pRpt->szResCode,strlen(pRpt->szResCode))
            ,pRpt->szRptDate
            ,pRpt->szEndTelco
            );


	memset(dbBuff	,0x00	,sizeof(dbBuff)); //CCL(dbBuff);
	
	ret = hRemoteSock.recvAllMsg(10);
	//ret = util.recvPacket(hRemoteSock,dbBuff,60,0);
	if( ret < 0 || ret == 0 )
	{
	    //20180829 ptnsn 20 -> 32
		logPrintR(0,"[ERR] RPT ack recv failed - ptnsn[%.32s]errno[%s]",rptData.szPtnsn,strerror(errno));

		/* report 재전송 */
		//retryReport(db,rptData);
		return -1;
	}

	mmsPacketBase.findValue((char*)hRemoteSock.getMsg(),"KEY",strKey);
	mmsPacketBase.findValue((char*)hRemoteSock.getMsg(),"CODE",strCode);
	
	logPrintR(1,"[INF] RPT ack recv mmsid[%lld]key[%s]rescode[%s]",nMsgId,strKey.c_str(),strCode.c_str());

	sum++;
	monitor.setDataTime();

	return 0;
}


int configParse(char* file)
{
	int  i;
	int  nRetFlag = TRUE;
	char *pszTmp;
	
	CKSConfig conf;
	Q_Entry *pEntry;
	//memset(&gConf,0x00,sizeof(gConf));

	// read mert conf
	if((pEntry = conf.qfDecoder(file)) == NULL)
	{
		printf("WARNING: Configuration file(%s) not found.\n", file);
		return -1;
	}

	conf.strncpy2(gConf.logonDBName , conf.FetchEntry("domain.logondb"),64);
	if( gConf.logonDBName == NULL )
	{
		strcpy(gConf.logonDBName,"");
	}
	conf.strncpy2(gConf.monitorName , conf.FetchEntry("domain.monitor"),64);
	if( gConf.monitorName == NULL )
	{
		strcpy(gConf.monitorName,"");
	}
	conf.strncpy2(gConf.domainPath , conf.FetchEntry("domain.self"),64);
	if( gConf.domainPath == NULL )
	{
		strcpy(gConf.domainPath,"");
	}
	gConf.socketLinkTime = conf.FetchEntryInt("socket.linktime");
	if( gConf.socketLinkTime <= 0 )
	{
		gConf.socketLinkTime = 1;
	}
	return 0;
}


void viewPackReport(char *a,int n)
{
	int i;
	char logMsg[VIEWPACK_MAX_SIZE];
	char strtmp[VIEWPACK_MAX_SIZE];

	memset(logMsg	,0x00	,sizeof(logMsg));
	memset(strtmp	,0x00	,sizeof(strtmp));

	for(i=0;i<n;i++)
	{
		if( a[i] == 0x00 )
		{
			strtmp[i] = '.';
		}
		else
		{
			memcpy(strtmp+i,a+i,1);
		}
	}

	sprintf(logMsg,"info:[%s]",strtmp);
	_monPrint(_MONILOG,logMsg);
	
	return ;
}



int retryReport(CKSSocket& db, CReportDbInfo& rptData)
{
	int ret;
	ret = db.connectDomain(reportDbDomainName);
	if( ret != 0 )
	{
		logPrintR(0,"[ERR] socket_domain reportDB:retryReport - connect failed ret[%s]",strerror(ret));
		db.close();
		return -1;
	}


	strcpy(rptData.header.msgType,"3");
	
	ret = db.send((char*)&rptData,sizeof(CReportDbInfo));

	if( ret != sizeof(CReportDbInfo))
	{
		logPrintR(0,"[ERR] socket_domain reportDB:retryReport - send failed send reportInfoSize[%d/%d]errno[%s]"
                ,ret
                ,sizeof(CReportDbInfo)
                ,strerror(errno)
                );
		return -1;
	}

	ret = db.select(5,0);
	if( ret == 0 )
	{
		logPrintR(0,"[ERR] socket_domain reportDB:retryReport - timeout 5 sec");
		return -1;
	}

	if( ret < 0 )
	{
		logPrintR(0,"[ERR] socket_domain reportDB:retryReport - select failed errno[%s]",strerror(errno));
		return -1;
	}

	ret = db.rcvmsg((char*)&rptData);
	if( ret == 0 || ret < 0 )
	{
		logPrintR(0,"[ERR] socket_doamin reportDB:retryReport - recv failed errno[%s]ret[%d]",strerror(errno),ret);
		return -1;
	}

	return 0;
}
