#ifndef _LOGON_SESSION_MMSB_H_
#define _LOGON_SESSION_MMSB_H_

#include <iostream>
#include "stdafx.h"
#include "smsPacketStruct.h"
#include "kssocket.h"
#include "logonDbInfo.h"
#include "ksconfig.h"
#include "senderDbInfo_PART.h"

#include "mmsPacketBase.h"
#include "mmsPacketSend.h"
#include "dbUtil.h"
#include "mmsFileProcess.h"

using namespace std;


class CConfigLogonSession {
    public:
        char serverIP[16];
        int serverPORT;
        int process_sleep_time;
        char logonDBName[64];
        char bindir[64];
        char cfgdir[64];
        char logPath[64];
        char domainPath[64];
        int dbRequestTimeOut;
        char ContentPath[64];
        char senderDBName[64];
        char smsTelcoInfo[64];
};

int activeProcess = TRUE;
struct _message_info    message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];


CConfigLogonSession gConf;

int getAccept(int hOpenSocket,char* szIP,int nPort);
int requestLogon(int sockfd,CLogonDbInfo &logonDbInfo);
int checkServerInfo(CLogonDbInfo &logonDbInfo,char* szIP,int nPort);
int checkLoginDup(CLogonDbInfo &logonDbInfo);
int configParse(char* file);



int classifyS(CKSSocket& db, char* buff, CMMSPacketSend& mmsPacketSend);
int getMMSID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID,char* cid);
int getCTNID2DB(CKSSocket& db, CSenderDbMMSID& senderDbMMSID,char* cid, CMMSPacketSend& mmsPacketSend);

int setMMSTBL2DB(CKSSocket& db,int nMMSId,int ctnid,
        int priority, CMMSPacketSend& mmsPacketSend, 
        CBcastData* pBcastData = NULL );

int setMMSMSG2DB(CKSSocket& db,char* szCid, int nMMSId,int ctnid,CMMSFileProcess& mmsFileProcess, int priority, int type, CMMSPacketSend& mmsPacketSend, CBcastData* pBcastData = NULL);

int setMMSCTNTBL2DB(CKSSocket& db,CMMSFileProcess& mmsFileProcess);


int getTelcoId(int imgCnt, char* szTelco, int nColorYN);

void writeLogMMSData(CMMSPacketSend& mmsPacketSend,int mmsid, int ctnid);

#endif


