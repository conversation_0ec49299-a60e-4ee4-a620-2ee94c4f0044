#ifndef _SENDER_PROCESS_H_
#define _SENDER_PROCESS_H_

#include <string>
#include "stdafx.h"
#include "smsPacketStruct.h"
#include "logonDbInfo.h"
#include "fifoInfo.h"
#include "logonUtil.h"
#include "kssocket.h"
#include "adminInfo.h"
#include "adminUtil.h"
#include "senderDbInfo.h"
#include "processInfo.h"
#include "monitor.h"
#include "ksconfig.h"
#include "senderInfo.h"

using namespace std;

class CConfigSender {
    public:
        char logonDBName[64];
        char monitorName[64];
        char domainPath[64];
        int socketLinkTimeOut;
        int dbRequestTimeOut;
        int domainDupMaxCount;
};


CConfigSender gConf;
CSenderInfo gSenderInfo;

int activeProcess = TRUE;
struct _message_info	message_info;
struct _shm_info *shm_info;
char PROCESS_NO[ 7], PROCESS_NAME[36];


void senderProcess(int sockfd,CLogonDbInfo& logonDbInfo);
void logPrintS(int type, const char *format, ...);
int classifyS(CMonitor& monitor,
        CProcessInfo& processInfo,
        CLogonDbInfo& logonDbInfo,
        CKSSocket& db,
        CKSSocket& hRemoteSock,
        char* buff);
int recvLink(CKSSocket& hRemoteSock,char* buff);
int recvSMS(CLogonDbInfo& logonDbInfo,
        CKSSocket& db,
        CKSSocket& hRemoteSock,
        char* buff);

int send2DB(CKSSocket& db,CSenderDbInfo& senderDbInfo,CSenderDbInfoAck& ack);

int configParse(char* file);

void viewPackSender(char *a,int n);



bool bSActive = true;
time_t SThisT,SLastTLink;
time_t monLastT;
int sum=0;
char szSenderID[16];
char _DATALOG[64];
char _MONILOG[64];

// char* senderDbDomainName =  "/user/neosms/domain/DB_sender_1";
char senderDbDomainName[64];



#endif

