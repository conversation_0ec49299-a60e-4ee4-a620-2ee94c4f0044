#ifndef _MONITOR_H_
#define _MONITOR_H_

#include "stdafx.h"
#include "kssocket.h"
#include "ksconfig.h"

class CMonitor {
    public:
        CMonitor();
        int Init(char* type,
                char* thread,
                char* processname,
                char* cid,
                int ptnid,
                char* connip);

        void setDataSum(int sum);
        int setCurDate();
        int setLinkTime();
        int setDataTime();
        int send(char* domainName);

    private:
        char type[16];       /* protocol type */
        char thread[16];
        /* logon3Sedner  / logon3Report /logon5Sender /logon5Report */
        char curdate[16];  /* 현재 시간 */
        char processname[32];
        char ospid[16];
        char connid[16];    /* 업체 id */
        char customptnid[16]; /* 엄제 ptnid */
        char newconnip[16]; /* 최근 접속 IP */
        char newstartT[16]; /* 시작 시간 */
        char newdataT[16]; /* 최근 데이터 시간 (send/report) */
        char newlinkT[16];      /* 최근 링크 메세지 시간 */
        char datasum[16];  /* 데이터 전송 합계 */
        char szDate[16]; /* 임시 변수 : get_timestring */






};


#endif

