//============================================================================
//============================================================================
// Name        : telco_skb_new.cpp
// Author      : KskyB Inc.
// Version     :
// Copyright   : Copyright@KSKYB
// Description : Hello World in C++, Ansi-style
//============================================================================

#include <iostream>
#include <map>
using namespace std;

#include <sys/timeb.h>

#include <signal.h>
#include <errno.h>
#include <pthread.h>

#include <unistd.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <signal.h>
#include <arpa/inet.h>
#include <sys/un.h>

#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>

#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/msg.h>

#include "Properties.h"
#include "SocketTCP.h"
#include "myException.h"
#include "Curl.h"
#include "json.h"
#include "alimTalkApi.h"
#include "PacketCtrlSKB_MMS.h"
#include "DatabaseORA_MMS.h"
#include <code_info.h>
#include <message_info.h>
#include <ml_ctrlsub.h>

// 최대 쓰레드 POOL 크기
#define MAX_THREAD_POOL 128
using namespace std;

typedef struct _basicInfo
{
	pthread_t tid;
	int inum;
	char quid[32];
	char qname[32];
	char target_url[128];
}basicInfo;

// 전역 쓰레드 구조체 
typedef struct _ph
{
	int data;    // 현재 사용중인 소켓 fd
	int index_num;// 인덱스 번호
}ph;

// 전역쓰레드 구조체로써 
// 현재 쓰레드 상황을 파악함
struct schedul_info
{
	map<string,string> mapSendMsg;// 가장최근에 만들어진 소켓지시자
	bool setFlag;
	multimap<int, ph> phinfo;
};

struct _message_info message_info;
struct _shm_info * shm_info;

schedul_info s_info;
sql_context  ctx;

pthread_cond_t * mycond; // 각 쓰레드별 조건변수
pthread_cond_t async_cond = PTHREAD_COND_INITIALIZER; // 쓰레드 동기화를 위한 조건변수

pthread_mutex_t mutex_lock    = PTHREAD_MUTEX_INITIALIZER; // 각 쓰레드별 조건변수의 크리티컬세션 지정을 위한 뮤텍스 
pthread_mutex_t mutex_db_lock = PTHREAD_MUTEX_INITIALIZER; // DB 접근 동기화용 mutex
pthread_mutex_t async_mutex   = PTHREAD_MUTEX_INITIALIZER; // 쓰레드 동기화용 조건변수의 크리티컬세션 지정을 위한 뮤텍스

//K-- 5Fab2018
volatile bool bFlagClosed = false;

int activeProcess = true;
char PROCESS_NO[7], PROCESS_NAME[36];
KSKYB::CProperties g_prop;
KSKYB::CDatabaseORA g_oracle;

void * procSendRept(void* param);
int    CheckThreadStatus(basicInfo** param, int nCnt);
void   Init_Server();
void   CloseProcess(int sig);
void   mnt(char *buf, int st, int err);
void   log(char *buf, int st, int err);
void 	 makeCurrentTime(string &cTime);
void   Eliminate(char *str, char *ch);
int    nThreadCnt = 0;

int main(int argc, char* argv[])
{
	int idx;
	char logMsg[1024];
	g_prop.load(argv[1]);

	int i;
	ph myph;
	int status;

	nThreadCnt = g_prop.getPropertyInt("gw.tcnt");
	int nServer = g_prop.getPropertyInt("gw.server");

	char quid[32] = {0x00,};
	sprintf(quid,"%s",g_prop.getProperty("gw.quid"));

	char qname[32] = {0x00,};
	sprintf(qname,"%s",g_prop.getProperty("gw.qname"));

	char targetUrl[128] = {0x00,};
	sprintf(targetUrl, "%s", g_prop.getProperty("gw.target_url"));

	Init_Server();

	printf("ALIMTALK THREAD CNT:[%d]\n", nThreadCnt);

	if (ml_sub_init(PROCESS_NO, PROCESS_NAME, (char*)0, (char*)0) < 0) 
	{
		sprintf(logMsg, "ml_sub_init ERROR.");
		mnt(logMsg, 0, 0);
		return 0;
	}

	if (g_oracle.setEnableThreads()<0)
	{
		sprintf(logMsg, "[%s():%d][setEnableThreads ERROR. process return;]", __func__, __LINE__);
		mnt(logMsg, 0, 0);

		return 0;
	}
	
	if (g_oracle.initThread(ctx) < 0 || ctx == NULL)
	{
		sprintf(logMsg, "[%s():%d][g_oracle.initThread Fail.]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		return -1;
	}

	if(g_oracle.connectToOracle(ctx, g_prop.getProperty("db.uid"), g_prop.getProperty("db.dsn")) < 0)
	{
		sprintf(logMsg, "[%s():%d][g_oracle.connectToOracle() Fail..]", __func__, __LINE__);
		mnt(logMsg, 0, 0);
		return -1;
	}

	curl_global_init(CURL_GLOBAL_DEFAULT);

	mycond = (pthread_cond_t *)malloc(sizeof(pthread_cond_t)*nThreadCnt); // 스레드 갯수만큼 조건변수 생성 
	s_info.mapSendMsg.clear(); // 스레드 전역변수 초기화
	basicInfo tBasicInfo[nThreadCnt];

	// 스레드 POOL 생성
	for(i = 0; i < nThreadCnt; i++)
	{
		memset((void *)&myph, 0x00, sizeof(myph));
		myph.index_num = i;
		s_info.phinfo.insert(pair<int, ph>(0, myph));
	
		tBasicInfo[i].inum = i;
		snprintf(tBasicInfo[i].quid, sizeof(tBasicInfo[i].quid), quid); 
		snprintf(tBasicInfo[i].qname, sizeof(tBasicInfo[i].qname), qname); 
		snprintf(tBasicInfo[i].target_url, sizeof(tBasicInfo[i].target_url), targetUrl); 

		if( 0 != pthread_cond_init(&mycond[i], NULL))
		{
			//sprintf(logMsg, "[TST]initialization error on condition variable (mycond[%d]): create treads", i);
			sprintf(logMsg, "[%s():%d][initialization error on condition variable (mycond[%d]): create treads]", __func__, __LINE__, i);
			mnt(logMsg, 0, 0);
			return -1;
			//exit(0);
		}
		// 조건변수를 이용해서 쓰레드간 동기화를 실시한다.
		if( 0 != pthread_mutex_lock(&async_mutex))
		{
			//sprintf(logMsg, "[TST]mutex lock error (async_mutex): create treads");
			sprintf(logMsg, "[%s():%d][mutex lock error (async_mutex): create treads]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return -1;
//			exit(0);
		}

		if( 0 != pthread_cond_wait(&async_cond, &async_mutex))
		{
			//sprintf(logMsg, "[TST]condition wait(async_cond): main; create threads");
			sprintf(logMsg, "[%s():%d][condition wait(async_cond): create threads]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return -1;
//			exit(0);
		}

		if( 0 != pthread_mutex_unlock(&async_mutex))
		{
			//sprintf(logMsg, "[TST]mutex unlock error (async_mutex): main; create treads");
			sprintf(logMsg, "[%s():%d][mutex unlock error (async_mutex): create treads]", __func__, __LINE__);
			mnt(logMsg, 0, 0);
			return -1;
//			exit(0);
		}
	}

	s_info.setFlag = true; //데이터 중복 가져가기 방지용 flag	

	time_t now, last;
	double diff;
	time(&last);

	while(activeProcess)
	{
		//check threads alive
		time(&now);
		diff = difftime(now, last);
		if(diff > 120)
		{
			if( 0)
			{
				if(CheckThreadStatus((basicInfo**)tBasicInfo, nThreadCnt) < 0)
					break;
			}
			CheckThreadStatus((basicInfo**)tBasicInfo, nThreadCnt);

			time(&last);
			usleep(100000);
		}

		//K-- 5Fab2018
		if( true == bFlagClosed)
		{
			//sprintf(logMsg, "[TST] Start Close process.");
			sprintf(logMsg, "[INF][Start Close process]");
			mnt(logMsg, 0, 0);
			
			multimap<int, ph>::iterator mi;
			while(1)
			{
#if 0 //DEBUG
				mi = s_info.phinfo.begin();
				sprintf(logMsg, "[TST] ------", mi->first);
				mnt(logMsg, 0, 0);
				int _k = 0;
				while( mi != s_info.phinfo.end())
				{
					sprintf(logMsg, "[TST] [%d] TP(main): mi value[%d] : thread No[%d]", _k, mi->first, mi->second.index_num);
					mnt(logMsg, 0, 0);
					++mi;
					++_k;
				}
#endif
				mi = s_info.phinfo.end();
				mi--;
				// 2. look into schedule_info
				if( 0 == mi->first)
				{
					curl_global_cleanup();
					//sprintf(logMsg, "[TST] Check schedule_info: end properly");
					sprintf(logMsg, "[INF][Check schedule_info: end properly]");
					mnt(logMsg, 0, 0);
					break;
				}
			} //while(1)
			activeProcess = false; // kill main thread
			continue;
		} // if( bFlagClosed)
		//K----------
		
		//0.02초마다 
		usleep(80000);
		map<string, string> mapSendMsg;
		
		while(true)
		{
			multimap<int, ph>::iterator mi;
			mi = s_info.phinfo.begin();

			continue;
			
			
			if(s_info.setFlag == false)
			{
				//cout << "data unavailable" << endl;
				//sprintf(logMsg, "[TST2]");
				//mnt(logMsg, 0, 0);
				continue;
			}

			//pthread_mutex_unlock(&mutex_lock);
			break;
		}
		

	}

	sprintf(logMsg, "[%s()][main Process End.]", __func__);
	mnt(logMsg, 0, 0);
	
	ml_sub_end();
	g_oracle.closeFromOracle(ctx);

	printf("alimtalk ml_sub_end\n");

	return 0;
}

int CheckThreadStatus(basicInfo** param, int nThreadCnt)
{
  char logMsg[256];
  int idx, status;
  KSKYB::CSocketTCP sockInst;
  basicInfo tpSub[nThreadCnt];
  memcpy(tpSub, param, sizeof(basicInfo) * nThreadCnt);

	sprintf(logMsg, "[%s()][thread check]", __func__);
	mnt(logMsg, 0, 0);

	for (idx = 0; idx < nThreadCnt; idx++) 
	{
		if (pthread_kill(tpSub[idx].tid, 0) != 0) 
		{
			pthread_join(tpSub[idx].tid, (void**)&status);
			//activeProcess = false;
			bFlagClosed = true;
			sprintf(logMsg, "[%s()][CheckThreadStatus fail]", __func__);
			mnt(logMsg, 0, 0);
			return -1;
		}
	}

	return 0;
}



void Init_Server()
{
	setpgrp();
	printf("Process Running.. Please wait 2 seconds.\n");
	sleep(2);
	signal(SIGHUP, CloseProcess);
	signal(SIGCLD, SIG_IGN);
	signal(SIGPIPE, SIG_IGN);
	signal(SIGTERM, CloseProcess); //15
	signal(SIGINT, CloseProcess);
	signal(SIGQUIT, CloseProcess);
	signal(SIGKILL, CloseProcess);
	signal(SIGSTOP, CloseProcess);
	signal(SIGUSR1, CloseProcess); //10
}

void CloseProcess(int sig)
{
	//K-- 5Fab2018 comment out
	//K--activeProcess = false;
	bFlagClosed = true;

	char logMsg[256];
	sprintf(logMsg,"CloseProcess Start & Exit [SIG:%d]\n", sig);
	mnt(logMsg, 0, 0);
	//K-- 08Fab2018 comment out
	//curl_global_cleanup();
}

void makeCurrentTime(string &cTime)
{
	time_t tm_time;
	struct tm *st_time;
	char buff[1024] = {0x00,};
	time(&tm_time);
	st_time = localtime(&tm_time);
	strftime(buff, 1024, "%Y%m%d%H%M%S", st_time);
	cTime = buff;
}

void log(char *buf, int st, int err)
{
	char log[1024] = {0x00,};
	snprintf(log, sizeof(log)-1, "[%s]%s", PROCESS_NAME, buf);

	if (ml_sub_send_log((char*)log, sizeof(log), 3, st, err) <= 0) {
		printf("%s ml_sub_send_log ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}	
}

void mnt(char *buf, int st, int err)
{
	char log[1024] = {0x00,};
	snprintf(log, sizeof(log)-1, "[%s]%s", PROCESS_NAME, buf);

	if (ml_sub_send_moni((char*)log, sizeof(log), 3, st, err) <= 0) {
		printf("%s ml_sub_send_moni ERROR. %d %s\n", PROCESS_NAME, errno, strerror(errno));
	}
}

