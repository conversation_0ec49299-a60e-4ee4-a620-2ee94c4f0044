/*
 * Properties.h
 *
 *  Created on: 2009. 8. 21
 *      Author: 임동규
 */

#include <string.h>
#include <string>     // std::string
#include <map>

using namespace std;


#ifndef PROPERTIES_H_
#define PROPERTIES_H_

namespace KSKYB {

typedef struct Q_Entry {
	char *name;
	char *value;
	struct Q_Entry *next;
} Q_Entry;

class CProperties
{
public:
	CProperties();
	virtual ~CProperties();

	void load(char* szFile);
	char* getProperty(const char* szKey);
	int getPropertyInt(const char* szKey);

private:
	Q_Entry *first;
	char* readFile(char *szFile, int *size);
	Q_Entry* decodeEntry(char *szEntry);
	char* removeSpace(char *str);
	char* makeWord(char* str, char stop);
};


class PartnerKeyManager {
private:
	map<string, string> partnerKeys;

public:
	void loadPartnerKeys(CProperties& g_prop) {
		// 파트너 키 문자열 가져오기
		char* rawPartnerKey = g_prop.getProperty("gw.partner_key");
		if (!rawPartnerKey) return;

		// 파트너 키들을 분리
		char* token = strtok(rawPartnerKey, "|");
		while (token != NULL) {
			char keyBuf[256] = {0};
			sprintf(keyBuf, "pk.%s", token);  // "pk.BC", "pk.WO" 형태로 만듦
			
			char* value = g_prop.getProperty(keyBuf);
			if (value) {
				partnerKeys[token] = value;  // "BC" -> "JeGn3mJIStCqs-OyoGKT"
			}
			
			token = strtok(NULL, "|");
		}
	}

	// 모든 파트너 키 값을 | 로 연결해서 반환
	string getAllPartnerKeyValues() {
		string result;
		map<string, string>::iterator it;
		for (it = partnerKeys.begin(); it != partnerKeys.end(); ++it) {
			if (!result.empty()) {
				result += "|";
			}
			//result += it->second;
			result += it->first + ":" + it->second;
		}
		return result;
	}

	// 특정 키에 대한 값 가져오기
	const char* getPartnerKeyValue(const char* key) {
		map<string, string>::iterator it = partnerKeys.find(key);
		if (it != partnerKeys.end()) {
			return it->second.c_str();
		}
		return NULL;
	}
};


}

#endif /* PROPERTIES_H_ */
