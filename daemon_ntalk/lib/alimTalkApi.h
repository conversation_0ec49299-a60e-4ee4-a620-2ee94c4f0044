/*
 * AllimtalkApi.h
 *
 * 2015.10.28
 * by <PERSON><PERSON>
 */

#ifndef _ALIMTALKAPI_H_
#define _ALIMTALKAPI_H

#include <iostream>
using namespace std;
#include <string>
#include <vector>
#include <cstdio>
#include "myException.h"
#include "json.h"


typedef struct st_talk_res
{
	//string received_at;
	//string code;
	//string message;
	string success;
	string transmissionId;
	string resultCode;
	string resultMessage;
}ST_TALK_RES;

typedef struct st_talk_polling_res
{
	string code;
	string response_id;
	string responsed_at;
	string message;
	
	st_talk_polling_res()
	{
		code = "";
		response_id = "";
		responsed_at = "";
		message = "";
	}
	
} ST_TALK_POLLING_RES;

typedef struct st_polling_success
{
	string sn;
	string status;
	string received_at;
	
	st_polling_success()
	{
		sn = "";
		status = "";
		received_at = "";
	}
} ST_POLLING_SUCCESS;

typedef struct st_polling_fail
{
	string sn;
	string status;
	string received_at;
	string message;
	
	st_polling_fail()
	{
		sn = "";
		status = "";
		received_at = "";
		message = "";
	}
} ST_POLLING_FAIL;

class CAlimtalkApi
{
public:
	CAlimtalkApi() {};
	~CAlimtalkApi() {};

	void makeSmsRequestMsg(const vector<string> &vtBuff, string &parameter, long long msgid);
	void makeSmsRequestMsgBt(map<string,string> &_mapSend, string &parameter, long long msgid);
	//void makeSmsRequestMsg_V3(map<string,string> &_mapSend, string &parameter, long long msgid, string &button_gb);
	void makeSmsRequestMsg_V3(map<string,string> &_mapSend, string &parameter, long long msgid);
	void makeSmsRequestMsg_V4(map<string,string> &_mapSend, string &parameter, long long msgid);
	void makePollingRequestMsg(string channelKey, string &parameter);
	int parsingResponse(string response, ST_TALK_RES &res);
	int euckrToUtf8(char *source, char *dest, int dest_size);
	void makeDateString(const string orgDate, string &dateString);
	
	int parsingPollingMsgResponse(string response, ST_TALK_POLLING_RES &res\
		                             , ST_POLLING_SUCCESS * _success, ST_POLLING_FAIL * _fail\
		                             , int & _successSize, int & _failSize);
};

#endif
