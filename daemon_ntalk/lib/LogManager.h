/**
 * @file	LogManager.h
 * @brief	Singleton base
 * <AUTHOR>
 * @date	2014-04-24
 */

#ifndef LogManager_h_
#define LogManager_h_
#include <string>

typedef enum _log_type
{	
	// 하위 로그는 상위 로그를 포함한다.
	log_debug = 0,	//  프로세스 로그					// 실제 데이터 값들
	log_trace = 1,		//  프로세스 로그					// 생성자 및 소멸자
	log_info  = 2,		//  프로세스 로그					// 데이터 흐름
	log_warning = 3,	//	프로세스 로그 
	log_session	= 4,	//	프로세스 로그
	log_error = 5,		//	프로세스 로그 / 모니터링 로그  
} log_type;

typedef struct _alert_config
{	
	int			alert;
	std::string alertAddr;
	std::string alertPort;
	std::string alertPage;
	std::string callType;
} alert_config;

// __func__, __LINE__
class LogManager
{
	LogManager(void);
public:
	~LogManager(void);

	static LogManager&	GetInstance();
/**
 * @ todo: string 객체 못받음 c_str()로 남겨야됨 불편할시 추후 확인 
 */
	int		Write(log_type t, const char *format, ... );
	void	EnableConsolLog(bool enable = true){consol_ = enable;};
	void	SetLogLevel(unsigned int l){level_ = (log_type)l;};
	void	SetAlert(alert_config e){alert_ = e;};

	void	SetProcessLog(void (*f)(std::string));
	void	SetMoniterLog(void (*f)(std::string));
	void	SetProcessName(std::string name){process_name_ = name;};
private:
	int		_strReplace(char* sourceMsg , char* a , char* b);
	int		_Alert(char* pForm,...);
	void	_write_console( const char* buf );
	std::string _get_ip();

	bool	consol_;

	void (*ProcessLog)(std::string);
	void (*MoniterLog)(std::string);
	log_type	level_;
	alert_config alert_;
	std::string process_name_;
};
#endif
