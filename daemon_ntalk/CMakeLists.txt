# CMakeLists.txt for daemon_ntalk
cmake_minimum_required(VERSION 3.10)
project(daemon_ntalk CXX)

set(CMAKE_CXX_STANDARD 11)

# CLion build flag configuration (optional for IDE)
if(NOT DEFINED ENABLE_CLION_BUILD)
    option(ENABLE_CLION_BUILD "Enable CLion-specific build configuration" ON)
endif()

if(ENABLE_CLION_BUILD)
    add_definitions(-DCLION_BUILD)
    message(STATUS "daemon_ntalk: CLion build mode enabled")
endif()

add_definitions(-DNOSEND)

# Directory path configuration
message(STATUS "CMAKE_SOURCE_DIR: ${CMAKE_SOURCE_DIR}")
message(STATUS "CMAKE_CURRENT_SOURCE_DIR: ${CMAKE_CURRENT_SOURCE_DIR}")

# Oracle environment configuration
set(ORACLE_HOME "/usr/lib/oracle/21/client64")
set(PROC_INCLUDE "/usr/include/oracle/21/client64")
set(PROC_CONFIG "/usr/lib/oracle/21/client64/lib/precomp/admin/pcscfg.cfg")

# Database configuration file loading
# Option 1: Environment variables (for CI/CD environments)
# Option 2: Local configuration file (for development)

# Include database config if exists
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    include("${CMAKE_CURRENT_SOURCE_DIR}/db_config.cmake")
    message(STATUS "Database config loaded from db_config.cmake")
endif()

# Read database configuration from environment variables (takes precedence over config file)
if(DEFINED ENV{SMS_DBSTRING})
    set(SMS_DBSTRING "$ENV{SMS_DBSTRING}")
endif()
if(DEFINED ENV{SMS_DBID})
    set(SMS_DBID "$ENV{SMS_DBID}")
endif()
if(DEFINED ENV{SMS_DBPASS})
    set(SMS_DBPASS "$ENV{SMS_DBPASS}")
endif()
if(DEFINED ENV{MMS_DBSTRING})
    set(MMS_DBSTRING "$ENV{MMS_DBSTRING}")
endif()
if(DEFINED ENV{MMS_DBID})
    set(MMS_DBID "$ENV{MMS_DBID}")
endif()
if(DEFINED ENV{MMS_DBPASS})
    set(MMS_DBPASS "$ENV{MMS_DBPASS}")
endif()

# Database connection option (default OFF for safe development environment)
option(ENABLE_DB_CONNECTION "Enable database connection for Pro*C compilation" ON)

# SQLCHECK mode configuration (environment variable takes precedence)
if(DEFINED ENV{PROC_SQLCHECK})
    set(PROC_SQLCHECK "$ENV{PROC_SQLCHECK}")
elseif(ENABLE_DB_CONNECTION)
    set(PROC_SQLCHECK "SEMANTICS")
else()
    set(PROC_SQLCHECK "SYNTAX")  # Default: SYNTAX (no database connection required)
endif()

# Required configuration validation (for SEMANTICS mode only)
if(PROC_SQLCHECK STREQUAL "SEMANTICS")
    if(NOT DEFINED SMS_DBSTRING OR SMS_DBSTRING STREQUAL "" OR SMS_DBSTRING STREQUAL "your_sms_database_string")
        message(FATAL_ERROR "SMS_DBSTRING not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED SMS_DBID OR SMS_DBID STREQUAL "" OR SMS_DBID STREQUAL "your_sms_database_id")
        message(FATAL_ERROR "SMS_DBID not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED SMS_DBPASS OR SMS_DBPASS STREQUAL "" OR SMS_DBPASS STREQUAL "your_sms_database_password")
        message(FATAL_ERROR "SMS_DBPASS not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED MMS_DBSTRING OR MMS_DBSTRING STREQUAL "" OR MMS_DBSTRING STREQUAL "your_mms_database_string")
        message(FATAL_ERROR "MMS_DBSTRING not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED MMS_DBID OR MMS_DBID STREQUAL "" OR MMS_DBID STREQUAL "your_mms_database_id")
        message(FATAL_ERROR "MMS_DBID not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    if(NOT DEFINED MMS_DBPASS OR MMS_DBPASS STREQUAL "" OR MMS_DBPASS STREQUAL "your_mms_database_password")
        message(FATAL_ERROR "MMS_DBPASS not set properly for SEMANTICS check. Either set environment variables or create db_config.cmake from template")
    endif()
    message(STATUS "SQLCHECK=SEMANTICS mode enabled - database connection required")
else()
    message(STATUS "SQLCHECK=SYNTAX mode enabled - no database connection required")
endif()

# Configuration loading confirmation (without exposing sensitive information)
message(STATUS "Database configuration loaded successfully")

# Directory configuration
set(ORG_D ${CMAKE_CURRENT_SOURCE_DIR})
set(BIN_D ${ORG_D}/bin)
set(OBJ_D ${ORG_D}/obj)
set(LIB_D ${ORG_D}/lib)
set(INC_D ${ORG_D}/inc)
set(SRC_D ${ORG_D}/src)

# External include directory path configuration
if(EXISTS "${CMAKE_SOURCE_DIR}/command_ntalk/inc")
    # Running from main project
    set(EXT_INC "${CMAKE_SOURCE_DIR}/command_ntalk/inc")
    message(STATUS "Include location: main project")
elseif(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../command_ntalk/inc")
    # Running from standalone project
    set(EXT_INC "${CMAKE_CURRENT_SOURCE_DIR}/../command_ntalk/inc")
    message(STATUS "Include location: standalone project")
else()
    # CLion project or other case (use NEONTK_230_ROOT_PATH variable if available)
    if(DEFINED NEONTK_230_ROOT_PATH)
        set(EXT_INC "${NEONTK_230_ROOT_PATH}/command_ntalk/inc")
    else()
        set(EXT_INC "/home/<USER>/CLionProjects/neontk_230/command_ntalk/inc")
    endif()
    message(STATUS "Include location: CLion or fallback path")
endif()

# External library object path configuration
if(EXISTS "${CMAKE_SOURCE_DIR}/command_ntalk/obj/sms_ctrlsub++.o")
    # Running from main project
    set(EXT_LIB "${CMAKE_SOURCE_DIR}/command_ntalk/obj/sms_ctrlsub++.o")
elseif(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../command_ntalk/obj/sms_ctrlsub++.o")
    # Running from standalone project
    set(EXT_LIB "${CMAKE_CURRENT_SOURCE_DIR}/../command_ntalk/obj/sms_ctrlsub++.o")
else()
    # CLion project or other case (use NEONTK_230_ROOT_PATH variable if available)
    if(DEFINED NEONTK_230_ROOT_PATH)
        set(EXT_LIB "${NEONTK_230_ROOT_PATH}/command_ntalk/obj/sms_ctrlsub++.o")
    else()
        set(EXT_LIB "/home/<USER>/CLionProjects/neontk_230/command_ntalk/obj/sms_ctrlsub++.o")
    endif()
    message(STATUS "Library location: CLion or fallback path (external object)")
endif()

# Path configuration confirmation
message(STATUS "EXT_INC: ${EXT_INC}")
message(STATUS "EXT_LIB: ${EXT_LIB}")

# Find Oracle Pro*C compiler
find_program(PROC_EXECUTABLE proc PATHS ${ORACLE_HOME}/bin)
if(NOT PROC_EXECUTABLE)
    message(FATAL_ERROR "Oracle Pro*C compiler not found. Please check ORACLE_HOME environment variable.")
endif()

# Compiler definitions and flags
add_definitions(
    -D_GNU_SOURCE
    -D_REENTRANT
)

# DEBUG mode configuration (enables debug output when DEBUG >= 5)
option(ENABLE_DEBUG "Enable debug output (DEBUG >= 5)" OFF)

# Debug flag configuration based on build type
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DDEBUG=5)
    message(STATUS "Debug build: DEBUG=5 enabled")
elseif(ENABLE_DEBUG)
    add_definitions(-DDEBUG=5)
    message(STATUS "Debug mode manually enabled (DEBUG=5)")
else()
    add_definitions(-DDEBUG=0)
    message(STATUS "Release build: DEBUG=0 (debug disabled)")
endif()

# Compiler flags configuration - separate string flags for better management
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -Wall")
# MMS mode flags separated for clarity
set(CMMS_FLAGS_LIST
    -g
    -Wall
    -D_MMS_MODE
)
